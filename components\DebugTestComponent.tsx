'use client';

import { useState, useEffect } from 'react';

interface User {
  id: number;
  name: string;
  email: string;
}

export default function DebugTestComponent() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [count, setCount] = useState(0);

  // 这个函数可以用来测试服务端调试
  const fetchUsers = async () => {
    try {
      setLoading(true);
      // 设置断点在这里测试客户端调试
      debugger; // 这会触发调试器暂停
      
      const response = await fetch('/api/debug-test');
      const data = await response.json();
      
      // 另一个断点位置
      console.log('Fetched users:', data);
      setUsers(data.users || []);
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  // 测试客户端状态更新
  const incrementCounter = () => {
    // 在这里设置断点来检查状态更新
    const newCount = count + 1;
    console.log('Incrementing count from', count, 'to', newCount);
    setCount(newCount);
  };

  // 测试副作用调试
  useEffect(() => {
    console.log('Component mounted, fetching users...');
    fetchUsers();
  }, []);

  // 测试条件渲染调试
  useEffect(() => {
    if (count > 5) {
      console.log('Count exceeded 5!', { count });
      // 可以在这里设置条件断点: count > 5
    }
  }, [count]);

  if (loading) {
    return (
      <div className="p-4 border rounded-lg">
        <h2 className="text-xl font-bold mb-4">调试测试组件</h2>
        <p>加载中...</p>
      </div>
    );
  }

  return (
    <div className="p-4 border rounded-lg space-y-4">
      <h2 className="text-xl font-bold">调试测试组件</h2>
      
      <div className="space-y-2">
        <p>计数器: {count}</p>
        <button 
          onClick={incrementCounter}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          增加计数 (+1)
        </button>
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-semibold">用户列表:</h3>
        {users.length > 0 ? (
          <ul className="space-y-1">
            {users.map(user => (
              <li key={user.id} className="p-2 bg-gray-100 rounded">
                {user.name} - {user.email}
              </li>
            ))}
          </ul>
        ) : (
          <p className="text-gray-500">没有用户数据</p>
        )}
      </div>

      <button 
        onClick={fetchUsers}
        className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
      >
        重新获取用户
      </button>

      <div className="text-sm text-gray-600 mt-4">
        <p><strong>调试提示:</strong></p>
        <ul className="list-disc list-inside space-y-1">
          <li>在 fetchUsers 函数中设置断点</li>
          <li>在 incrementCounter 函数中设置断点</li>
          <li>使用条件断点: count > 5</li>
          <li>检查网络请求到 /api/debug-test</li>
        </ul>
      </div>
    </div>
  );
}
