import { NextRequest, NextResponse } from 'next/server';

// 模拟用户数据
const mockUsers = [
  { id: 1, name: '张三', email: '<PERSON><PERSON><PERSON>@example.com' },
  { id: 2, name: '李四', email: '<EMAIL>' },
  { id: 3, name: '王五', email: '<EMAIL>' },
];

export async function GET(request: NextRequest) {
  try {
    // 在这里设置断点来调试服务端代码
    console.log('Debug API called at:', new Date().toISOString());
    
    // 模拟一些处理逻辑
    const searchParams = request.nextUrl.searchParams;
    const limit = searchParams.get('limit');
    const delay = searchParams.get('delay');
    
    // 可以在这里设置断点检查请求参数
    console.log('Request params:', { limit, delay });
    
    // 模拟延迟（用于测试异步调试）
    if (delay) {
      const delayMs = parseInt(delay, 10) || 1000;
      console.log(`Simulating delay of ${delayMs}ms`);
      await new Promise(resolve => setTimeout(resolve, delayMs));
    }
    
    // 处理限制参数
    let users = mockUsers;
    if (limit) {
      const limitNum = parseInt(limit, 10);
      if (limitNum > 0) {
        users = mockUsers.slice(0, limitNum);
      }
    }
    
    // 在返回前设置断点检查最终数据
    console.log('Returning users:', users);
    
    return NextResponse.json({
      success: true,
      users,
      timestamp: new Date().toISOString(),
      requestInfo: {
        method: request.method,
        url: request.url,
        headers: Object.fromEntries(request.headers.entries()),
      }
    });
    
  } catch (error) {
    // 错误处理调试点
    console.error('Error in debug API:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 调试 POST 请求
    console.log('POST request body:', body);
    
    // 模拟创建新用户
    const newUser = {
      id: mockUsers.length + 1,
      name: body.name || '新用户',
      email: body.email || '<EMAIL>',
    };
    
    console.log('Created new user:', newUser);
    
    return NextResponse.json({
      success: true,
      user: newUser,
      message: '用户创建成功',
      timestamp: new Date().toISOString(),
    });
    
  } catch (error) {
    console.error('Error in POST debug API:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}
