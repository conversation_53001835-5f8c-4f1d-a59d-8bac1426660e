/**
 * Test Endpoint for Email Verification i18n Support
 * Tests the fixed email verification code functionality with language detection
 */

import { NextRequest, NextResponse } from 'next/server';

import { getUserEmailLanguage } from '@/lib/email-language-detection';
import { getEmailTranslations } from '@/lib/email-translations';
import { sendEmail } from '@/lib/email';
import {
  verificationCodeTemplateI18n,
  type VerificationCodeData,
} from '@/lib/email-templates/verification-code-i18n';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      userId = 'test-user-123',
      email = '<EMAIL>',
      name = 'Test User',
      testLanguage,
      action = 'verify-current-email'
    } = body;

    // Test language detection
    const detectedLanguage = testLanguage || await getUserEmailLanguage(
      userId,
      request,
      'zh'
    );

    // Get translations for the detected language
    const translations = getEmailTranslations(detectedLanguage);

    // Generate test verification code
    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();

    // Prepare email data
    const emailData: VerificationCodeData = {
      userName: name,
      userEmail: email,
      verificationCode,
      expiresIn: 10,
      action: action as any,
      language: detectedLanguage,
    };

    // Determine subject based on action
    let subject: string;
    switch (action) {
      case 'verify-current-email':
        subject = translations.notifications.verification.verifyEmail.subject;
        break;
      case 'change-email':
        subject = translations.notifications.verification.changeEmail.subject;
        break;
      case 'login':
        subject = translations.notifications.verification.login.subject;
        break;
      case 'register':
        subject = translations.notifications.verification.register.subject;
        break;
      case 'reset-password':
        subject = translations.notifications.verification.resetPassword.subject;
        break;
      default:
        subject = translations.notifications.verification.verifyEmail.subject;
    }

    // Send test email
    const emailResult = await sendEmail({
      to: email,
      subject,
      html: verificationCodeTemplateI18n(emailData),
    });

    return NextResponse.json({
      success: true,
      message: 'Email verification test completed',
      testResults: {
        detectedLanguage,
        emailSent: !!emailResult,
        verificationCode, // Only for testing - never expose in production
        emailData: {
          ...emailData,
          verificationCode: '******', // Mask in response
        },
        subject,
        languageDetectionSource: 'test_parameter_or_detection',
      },
      languageTest: {
        requestedLanguage: testLanguage,
        detectedLanguage,
        translationsUsed: {
          subject,
          brandName: translations.common.brandName,
          greeting: translations.common.greeting,
        },
      },
    });

  } catch (error) {
    console.error('Email verification test error:', error);
    return NextResponse.json(
      { 
        error: 'Test failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Email Verification i18n Test Endpoint',
    usage: {
      method: 'POST',
      body: {
        userId: 'string (optional, default: test-user-123)',
        email: 'string (optional, default: <EMAIL>)',
        name: 'string (optional, default: Test User)',
        testLanguage: 'zh | en (optional, uses language detection if not provided)',
        action: 'verify-current-email | change-email | login | register | reset-password (optional, default: verify-current-email)',
      },
    },
    examples: {
      chineseTest: {
        userId: 'test-user-zh',
        email: '<EMAIL>',
        name: '测试用户',
        testLanguage: 'zh',
        action: 'verify-current-email',
      },
      englishTest: {
        userId: 'test-user-en',
        email: '<EMAIL>',
        name: 'Test User',
        testLanguage: 'en',
        action: 'change-email',
      },
      autoDetectionTest: {
        userId: 'test-user-auto',
        email: '<EMAIL>',
        name: 'Auto Detection User',
        // No testLanguage - will use language detection
        action: 'verify-current-email',
      },
    },
    testScenarios: [
      'Test with explicit Chinese language',
      'Test with explicit English language',
      'Test with Accept-Language header detection',
      'Test with user registration language from database',
      'Test with fallback to default language',
      'Test different verification actions',
    ],
  });
}
