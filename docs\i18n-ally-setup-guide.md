# i18n Ally VSCode 扩展配置指南

## 概述

本指南详细说明了如何为 RefundGo 项目配置 i18n Ally VSCode 扩展，以便与 next-intl 框架协同工作。

## 配置文件

### 1. VSCode 设置 (`.vscode/settings.json`)

```json
{
  "i18n-ally.localesPaths": ["messages"],
  "i18n-ally.keystyle": "nested",
  "i18n-ally.pathMatcher": "{locale}/{namespaces}.json",
  "i18n-ally.namespace": true,
  "i18n-ally.enabledParsers": ["json"],
  "i18n-ally.sourceLanguage": "zh",
  "i18n-ally.displayLanguage": "en",
  "i18n-ally.enabledFrameworks": ["next-intl", "react"],
  "i18n-ally.extract.autoDetect": true,
  "i18n-ally.extract.keygenStyle": "camelCase"
}
```

### 2. i18n Ally 配置文件 (`i18n-ally.config.js`)

项目根目录下的配置文件提供了更详细的设置，包括：
- 翻译引擎配置
- 自动提取规则
- 文件忽略规则
- 验证规则

## 文件结构

```
messages/
├── zh/                 # 中文翻译文件
│   ├── common.json
│   ├── homepage.json
│   ├── auth.json
│   └── ...
└── en/                 # 英文翻译文件
    ├── common.json
    ├── homepage.json
    ├── auth.json
    └── ...
```

## 主要功能

### 1. 翻译键提示和自动完成
- 在代码中输入 `t('` 时会显示可用的翻译键
- 支持嵌套键的点号语法，如 `t('navigation.login')`

### 2. 内联翻译显示
- 在代码中直接显示翻译内容
- 支持多语言切换查看

### 3. 翻译文件管理
- 侧边栏显示所有翻译键
- 支持添加、编辑、删除翻译
- 显示缺失的翻译

### 4. 自动提取
- 从代码中自动提取硬编码的字符串
- 生成对应的翻译键

## 使用方法

### 1. 安装扩展
在 VSCode 中搜索并安装 "i18n Ally" 扩展。

### 2. 重启 VSCode
安装完成后重启 VSCode 以加载配置。

### 3. 验证配置
运行验证脚本检查配置是否正确：
```bash
node scripts/validate-i18n-ally.js
```

### 4. 开始使用
- 打开任何包含翻译的文件
- 在侧边栏查看 "i18n Ally" 面板
- 开始编辑翻译或添加新的翻译键

## 常见问题

### Q: 扩展无法识别翻译文件
**A:** 检查以下配置：
1. `i18n-ally.localesPaths` 是否指向正确的目录
2. `i18n-ally.pathMatcher` 是否匹配文件结构
3. 翻译文件是否为有效的 JSON 格式

### Q: 翻译键提示不工作
**A:** 确保：
1. `i18n-ally.enabledFrameworks` 包含 "next-intl"
2. `i18n-ally.keystyle` 设置为 "nested"
3. 重启 VSCode

### Q: 显示语言不正确
**A:** 检查 `i18n-ally.displayLanguage` 设置，确保设置为您希望显示的语言。

## 验证和维护

### 运行验证脚本
```bash
node scripts/validate-i18n-ally.js
```

该脚本会检查：
- 目录结构
- 配置文件
- 翻译文件格式
- 键结构一致性

### 定期维护
1. 定期运行验证脚本
2. 检查并修复缺失的翻译
3. 保持翻译文件结构一致

## 相关文档

- [next-intl 官方文档](https://next-intl-docs.vercel.app/)
- [i18n Ally 扩展文档](https://github.com/lokalise/i18n-ally)
- [项目 i18n 配置](../src/i18n/)
