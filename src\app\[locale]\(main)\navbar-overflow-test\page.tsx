'use client';

import { RefundGoLogoHomepage } from '@/components/refund-go-logo';
import { cn } from '@/lib/utils';
import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';

export default function NavbarOverflowTestPage() {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  const isDark = theme === 'dark';

  return (
    <div className="min-h-screen bg-background">
      {/* Test Navigation Bar */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-nav-bg/80 dark:bg-nav-bg/80 backdrop-blur-md border-b border-border">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo Container with Visual Boundaries */}
            <div className="flex items-center h-full border-2 border-red-500/30 bg-red-50/20 dark:bg-red-900/20">
              <RefundGoLogoHomepage
                variant="navbar"
                className="bg-transparent shadow-none hover:shadow-xl"
                darkMode={isDark}
              />
            </div>
            
            {/* Navigation Items */}
            <div className="flex items-center space-x-4">
              <span className="text-sm text-muted-foreground">Theme: {theme}</span>
              <button
                type="button"
                onClick={() => setTheme(isDark ? 'light' : 'dark')}
                className="bg-primary text-primary-foreground px-3 py-1 rounded text-sm"
              >
                Toggle
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Content */}
      <div className="pt-20 pb-12">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-foreground mb-4">Navbar Logo Overflow Test</h1>
            <p className="text-lg text-muted-foreground">
              Testing the RefundGoLogoHomepage component overflow fix in navigation bar
            </p>
          </div>

          <div className="grid gap-8">
            {/* Test 1: Navigation Bar Height Verification */}
            <div className="bg-card p-8 rounded-xl border">
              <h2 className="text-2xl font-semibold mb-6 text-card-foreground">Navigation Bar Height Test</h2>
              <p className="text-muted-foreground mb-6">
                The red border above shows the logo container boundaries. Logo should not extend beyond the navigation bar.
              </p>
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <div className="w-4 h-4 bg-red-500"></div>
                  <span className="text-sm">Red border indicates logo container boundaries</span>
                </div>
                <div className="flex items-center gap-4">
                  <div className="w-4 h-4 bg-green-500"></div>
                  <span className="text-sm">Logo should fit completely within the red boundaries</span>
                </div>
              </div>
            </div>

            {/* Test 2: Logo Variants Comparison */}
            <div className="bg-card p-8 rounded-xl border">
              <h2 className="text-2xl font-semibold mb-6 text-card-foreground">Logo Variants Comparison</h2>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-3">Navbar Variant (Fixed)</h3>
                  <div className="border-2 border-green-500/30 bg-green-50/20 dark:bg-green-900/20 p-4 inline-block">
                    <RefundGoLogoHomepage
                      variant="navbar"
                      className="bg-transparent shadow-none hover:shadow-xl"
                      darkMode={isDark}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground mt-2">
                    Height: ~48px max, fits within h-16 (64px) navbar
                  </p>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium mb-3">Default Variant (Original)</h3>
                  <div className="border-2 border-orange-500/30 bg-orange-50/20 dark:bg-orange-900/20 p-4 inline-block">
                    <RefundGoLogoHomepage
                      variant="default"
                      className="bg-transparent shadow-none hover:shadow-xl"
                      darkMode={isDark}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground mt-2">
                    Height: ~56px+, would overflow in navbar
                  </p>
                </div>
              </div>
            </div>

            {/* Test 3: Responsive Behavior */}
            <div className="bg-card p-8 rounded-xl border">
              <h2 className="text-2xl font-semibold mb-6 text-card-foreground">Responsive Behavior Test</h2>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-3">Desktop (Current View)</h3>
                  <div className="border border-border p-4 rounded">
                    <div className="flex items-center justify-between h-16 bg-nav-bg/80 dark:bg-nav-bg/80 px-4 rounded">
                      <RefundGoLogoHomepage
                        variant="navbar"
                        className="bg-transparent shadow-none hover:shadow-xl"
                        darkMode={isDark}
                      />
                      <span className="text-sm text-muted-foreground">Desktop Navigation</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium mb-3">Mobile Simulation</h3>
                  <div className="border border-border p-4 rounded max-w-sm">
                    <div className="flex items-center justify-between h-16 bg-nav-bg/80 dark:bg-nav-bg/80 px-2 rounded">
                      <RefundGoLogoHomepage
                        variant="navbar"
                        className="bg-transparent shadow-none hover:shadow-xl"
                        darkMode={isDark}
                      />
                      <span className="text-xs text-muted-foreground">Mobile</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Test 4: Animation Overflow Test */}
            <div className="bg-card p-8 rounded-xl border">
              <h2 className="text-2xl font-semibold mb-6 text-card-foreground">Animation Overflow Test</h2>
              <p className="text-muted-foreground mb-6">
                Hover over the logo to test animations. The logo should not overflow during hover states.
              </p>
              <div className="border-2 border-blue-500/30 bg-blue-50/20 dark:bg-blue-900/20 p-4 inline-block">
                <RefundGoLogoHomepage
                  variant="navbar"
                  className="bg-transparent shadow-none hover:shadow-xl"
                  darkMode={isDark}
                />
              </div>
              <div className="mt-4 space-y-2 text-sm text-muted-foreground">
                <div>✅ Gradient sweep animation should stay contained</div>
                <div>✅ Icon rotation should not cause overflow</div>
                <div>✅ Text color transitions should work smoothly</div>
                <div>✅ Shadow effects should not extend beyond boundaries</div>
              </div>
            </div>

            {/* Test 5: Dark/Light Mode Consistency */}
            <div className="bg-card p-8 rounded-xl border">
              <h2 className="text-2xl font-semibold mb-6 text-card-foreground">Theme Consistency Test</h2>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium mb-3">Light Mode</h3>
                  <div className="bg-white border border-gray-200 p-4 rounded">
                    <div className="flex items-center h-16 px-4">
                      <RefundGoLogoHomepage
                        variant="navbar"
                        className="bg-transparent shadow-none hover:shadow-xl"
                        darkMode={false}
                      />
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium mb-3">Dark Mode</h3>
                  <div className="bg-gray-900 border border-gray-700 p-4 rounded">
                    <div className="flex items-center h-16 px-4">
                      <RefundGoLogoHomepage
                        variant="navbar"
                        className="bg-transparent shadow-none hover:shadow-xl"
                        darkMode={true}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Test 6: Layout Impact Test */}
            <div className="bg-card p-8 rounded-xl border">
              <h2 className="text-2xl font-semibold mb-6 text-card-foreground">Layout Impact Test</h2>
              <p className="text-muted-foreground mb-6">
                Testing that the logo fix doesn't affect other navigation elements
              </p>
              <div className="border border-border rounded p-4">
                <div className="flex items-center justify-between h-16 bg-nav-bg/80 dark:bg-nav-bg/80 px-4 rounded">
                  <RefundGoLogoHomepage
                    variant="navbar"
                    className="bg-transparent shadow-none hover:shadow-xl"
                    darkMode={isDark}
                  />
                  <div className="flex items-center space-x-4">
                    <span className="text-sm">Features</span>
                    <span className="text-sm">Pricing</span>
                    <span className="text-sm">About</span>
                    <button className="bg-primary text-primary-foreground px-3 py-1 rounded text-sm">
                      Sign In
                    </button>
                  </div>
                </div>
              </div>
              <p className="text-sm text-muted-foreground mt-4">
                All navigation elements should remain properly aligned and accessible.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
