import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import prisma from '@/lib/db';
import {
  sendVerificationCodeEmail,
  generateVerificationCode,
} from '@/lib/email';

// 验证码发送请求
const sendCodeSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址'),
});

// 验证码生成函数已从 @/lib/email 导入

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validation = sendCodeSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: '邮箱地址格式不正确' },
        { status: 400 },
      );
    }

    const { email } = validation.data;

    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      return NextResponse.json({ error: '该邮箱未注册' }, { status: 404 });
    }

    // 生成验证码
    const code = generateVerificationCode();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10分钟后过期

    // 使用特殊的identifier格式来标识这是登录验证码
    const loginIdentifier = `login:${email}`;

    // 删除该邮箱的旧登录验证码
    await prisma.verificationToken.deleteMany({
      where: { identifier: loginIdentifier },
    });

    // 保存新验证码到数据库
    await prisma.verificationToken.create({
      data: {
        identifier: loginIdentifier,
        token: code,
        expires: expiresAt,
      },
    });

    // 使用内置邮件系统发送验证码 (with language detection)
    const emailResult = await sendVerificationCodeEmail(
      email,
      {
        userName: user.name || '用户',
        userEmail: email,
        verificationCode: code,
        action: 'login',
        expiresIn: 10,
      },
      {
        userId: user.id,
        request: request,
      }
    );

    if (!emailResult.success) {
      console.error('发送邮件失败:', emailResult.error);
      return NextResponse.json(
        { error: '邮件发送失败，请稍后重试' },
        { status: 500 },
      );
    }

    return NextResponse.json({
      success: true,
      message: '验证码已发送，请查收邮件',
    });
  } catch (error) {
    console.error('发送验证码失败:', error);
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 });
  }
}
