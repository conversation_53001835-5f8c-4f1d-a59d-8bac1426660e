/**
 * Test Endpoint for URL Locale Detection Fix
 * Tests the fixed language detection system with different URL scenarios
 */

import { NextRequest, NextResponse } from 'next/server';

import { getUserEmailLanguage } from '@/lib/email-language-detection';
import { getEmailTranslations } from '@/lib/email-translations';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      userId = 'test-user-123',
      simulateReferer,
      simulateAcceptLanguage,
      testScenario = 'url-locale-detection'
    } = body;

    // Create a mock request with custom headers for testing
    const mockHeaders = new Headers(request.headers);
    
    // Set custom Referer header if provided
    if (simulateReferer) {
      mockHeaders.set('referer', simulateReferer);
    }
    
    // Set custom Accept-Language header if provided, or remove it if empty string
    if (simulateAcceptLanguage !== undefined) {
      if (simulateAcceptLanguage === '') {
        mockHeaders.delete('accept-language');
      } else {
        mockHeaders.set('accept-language', simulateAcceptLanguage);
      }
    }

    // Create mock request object
    const mockRequest = {
      ...request,
      headers: mockHeaders,
      url: request.url,
    } as NextRequest;

    // Test language detection
    const detectedLanguage = await getUserEmailLanguage(
      userId,
      mockRequest,
      'zh'
    );

    // Get translations for detected language
    const translations = getEmailTranslations(detectedLanguage);

    // Analyze the detection process
    const analysisResults = {
      testScenario,
      inputs: {
        userId,
        requestUrl: request.url,
        refererHeader: mockHeaders.get('referer'),
        acceptLanguageHeader: mockHeaders.get('accept-language'),
      },
      detection: {
        detectedLanguage,
        expectedFromReferer: simulateReferer ? extractLanguageFromRefererTest(simulateReferer) : null,
        expectedFromAcceptLanguage: simulateAcceptLanguage ? parseAcceptLanguageTest(simulateAcceptLanguage) : null,
      },
      translations: {
        verifyEmailSubject: translations.notifications.verification.verifyEmail.subject,
        changeEmailSubject: translations.notifications.verification.changeEmail.subject,
        brandName: translations.common.brandName,
        greeting: translations.common.greeting,
      },
    };

    return NextResponse.json({
      success: true,
      message: 'URL locale detection test completed',
      results: analysisResults,
    });

  } catch (error) {
    console.error('URL locale detection test error:', error);
    return NextResponse.json(
      { 
        error: 'Test failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    );
  }
}

// Helper function to test language extraction from referer URL
function extractLanguageFromRefererTest(refererUrl: string): string | null {
  try {
    const urlObj = new URL(refererUrl);
    const pathSegments = urlObj.pathname.split('/').filter(Boolean);
    
    if (pathSegments.length > 0) {
      const firstSegment = pathSegments[0].toLowerCase();
      if (firstSegment === 'en') return 'en';
      if (firstSegment === 'zh') return 'zh';
    }
    return null;
  } catch {
    return null;
  }
}

// Helper function to test Accept-Language parsing
function parseAcceptLanguageTest(acceptLanguage: string): string | null {
  try {
    const languages = acceptLanguage
      .split(',')
      .map(lang => {
        const [code, qValue] = lang.trim().split(';q=');
        const quality = qValue ? parseFloat(qValue) : 1.0;
        return { code: code.toLowerCase(), quality };
      })
      .sort((a, b) => b.quality - a.quality);

    for (const { code } of languages) {
      if (code.startsWith('en')) return 'en';
      if (code.startsWith('zh')) return 'zh';
    }
    return null;
  } catch {
    return null;
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'URL Locale Detection Test Endpoint',
    description: 'Tests the fixed language detection system with URL locale support',
    usage: {
      method: 'POST',
      body: {
        userId: 'string (optional, default: test-user-123)',
        simulateReferer: 'string (optional, simulates the Referer header)',
        simulateAcceptLanguage: 'string (optional, simulates Accept-Language header)',
        testScenario: 'string (optional, description of test scenario)',
      },
    },
    testScenarios: [
      {
        name: 'Chinese URL Locale',
        description: 'Test detection from /zh/ URL path',
        payload: {
          simulateReferer: 'https://refundgo.org/zh/dashboard',
          testScenario: 'chinese-url-locale',
        },
        expectedResult: 'zh',
      },
      {
        name: 'English URL Locale',
        description: 'Test detection from /en/ URL path',
        payload: {
          simulateReferer: 'https://refundgo.org/en/settings',
          testScenario: 'english-url-locale',
        },
        expectedResult: 'en',
      },
      {
        name: 'Accept-Language Priority',
        description: 'Test Accept-Language header takes priority over URL',
        payload: {
          simulateReferer: 'https://refundgo.org/zh/dashboard',
          simulateAcceptLanguage: 'en-US,en;q=0.9',
          testScenario: 'accept-language-priority',
        },
        expectedResult: 'en (Accept-Language overrides URL)',
      },
      {
        name: 'URL Fallback',
        description: 'Test URL locale when no Accept-Language',
        payload: {
          simulateReferer: 'https://refundgo.org/zh/profile',
          simulateAcceptLanguage: '', // Empty Accept-Language
          testScenario: 'url-fallback',
        },
        expectedResult: 'zh',
      },
      {
        name: 'No Locale Indicators',
        description: 'Test fallback when no locale indicators present',
        payload: {
          simulateReferer: 'https://refundgo.org/dashboard', // No locale in URL
          simulateAcceptLanguage: '', // No Accept-Language
          testScenario: 'fallback-test',
        },
        expectedResult: 'zh (fallback)',
      },
    ],
    examples: {
      chineseUrlTest: {
        simulateReferer: 'https://refundgo.org/zh/dashboard',
        testScenario: 'chinese-url-locale-test',
      },
      englishUrlTest: {
        simulateReferer: 'https://refundgo.org/en/settings',
        testScenario: 'english-url-locale-test',
      },
      priorityTest: {
        simulateReferer: 'https://refundgo.org/zh/dashboard',
        simulateAcceptLanguage: 'en-US,en;q=0.9',
        testScenario: 'priority-hierarchy-test',
      },
    },
  });
}
