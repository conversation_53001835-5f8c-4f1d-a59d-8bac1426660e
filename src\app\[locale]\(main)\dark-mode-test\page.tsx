'use client';

import { RefundGoLogoHomepage } from '@/components/refund-go-logo';
import { cn } from '@/lib/utils';
import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';

export default function DarkModeTestPage() {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  const isDark = theme === 'dark';

  return (
    <div className="min-h-screen bg-background py-12">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-foreground mb-4">Dark Mode Logo Test</h1>
          <p className="text-lg text-muted-foreground mb-6">
            Current theme: <strong>{theme}</strong> | isDark: <strong>{isDark.toString()}</strong>
          </p>
          <button
            onClick={() => setTheme(isDark ? 'light' : 'dark')}
            className="bg-primary text-primary-foreground px-4 py-2 rounded-lg mb-8"
          >
            Switch to {isDark ? 'Light' : 'Dark'} Mode
          </button>
        </div>

        <div className="space-y-8">
          {/* Test 1: Current Implementation */}
          <div className="bg-card p-8 rounded-xl border">
            <h2 className="text-2xl font-semibold mb-6 text-card-foreground">Current Implementation</h2>
            <p className="text-muted-foreground mb-6">
              This should show the current logo with darkMode={isDark.toString()}
            </p>
            <div className="flex justify-center space-x-8">
              <div className="text-center">
                <h3 className="text-sm font-medium mb-2">Default Variant</h3>
                <RefundGoLogoHomepage
                  variant="default"
                  className="bg-transparent shadow-none hover:shadow-xl"
                  darkMode={isDark}
                />
              </div>
              <div className="text-center">
                <h3 className="text-sm font-medium mb-2">Navbar Variant</h3>
                <RefundGoLogoHomepage
                  variant="navbar"
                  className="bg-transparent shadow-none hover:shadow-xl"
                  darkMode={isDark}
                />
              </div>
            </div>
          </div>

          {/* Test 2: Force Dark Mode */}
          <div className="bg-gray-900 p-8 rounded-xl">
            <h2 className="text-2xl font-semibold mb-6 text-white">Force Dark Mode (darkMode=true)</h2>
            <p className="text-gray-300 mb-6">
              This should always show white text regardless of theme
            </p>
            <div className="flex justify-center">
              <RefundGoLogoHomepage
                className="bg-transparent shadow-none hover:shadow-xl"
                darkMode={true}
              />
            </div>
          </div>

          {/* Test 3: Force Light Mode */}
          <div className="bg-gray-100 p-8 rounded-xl">
            <h2 className="text-2xl font-semibold mb-6 text-gray-900">Force Light Mode (darkMode=false)</h2>
            <p className="text-gray-600 mb-6">
              This should always show dark text regardless of theme
            </p>
            <div className="flex justify-center">
              <RefundGoLogoHomepage
                className="bg-transparent shadow-none hover:shadow-xl"
                darkMode={false}
              />
            </div>
          </div>

          {/* Test 4: Navigation Bar Simulation */}
          <div className="bg-card border rounded-xl">
            <h2 className="text-2xl font-semibold p-6 pb-0 text-card-foreground">Navigation Bar Simulation</h2>
            <p className="text-muted-foreground px-6 pb-4">
              Simulating the actual navigation bar environment - Text should be visible without hover
            </p>
            <nav className="bg-nav-bg/80 backdrop-blur-md border-b border-border p-4">
              <div className="flex items-center justify-between">
                <RefundGoLogoHomepage
                  className="bg-transparent shadow-none hover:shadow-xl"
                  darkMode={isDark}
                />
                <div className="text-sm text-muted-foreground">
                  Theme: {theme} | Should see: {isDark ? 'White "Refund" + Blue "Go"' : 'Dark "Refund" + Blue "Go"'}
                </div>
              </div>
            </nav>
          </div>

          {/* Test 5: Exact ModernNavbar Replication */}
          <div className="bg-card border rounded-xl">
            <h2 className="text-2xl font-semibold p-6 pb-0 text-card-foreground">Exact ModernNavbar Replication</h2>
            <p className="text-muted-foreground px-6 pb-4">
              Replicating the exact ModernNavbar component structure and classes
            </p>
            <nav className={cn(
              'fixed inset-x-0 top-0 z-50 w-full transition-all',
              'bg-nav-bg/80 dark:bg-nav-bg/80 backdrop-blur-md'
            )}>
              <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
                <div className='flex items-center justify-between h-16'>
                  <div className='flex items-center h-full max-h-12 overflow-hidden'>
                    <RefundGoLogoHomepage
                      variant="navbar"
                      className="bg-transparent shadow-none hover:shadow-xl"
                      darkMode={isDark}
                    />
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Exact navbar replication with overflow fix
                  </div>
                </div>
              </div>
            </nav>
            <div className="h-16"></div> {/* Spacer for fixed nav */}
          </div>

          {/* Test 6: CSS Debug Info */}
          <div className="bg-muted p-8 rounded-xl">
            <h2 className="text-2xl font-semibold mb-6 text-foreground">CSS Debug Info</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium mb-3">Expected Colors</h3>
                <div className="space-y-2 text-sm">
                  <div>Dark Mode "Refund": <span className="text-white bg-gray-800 px-2 py-1 rounded">text-white</span></div>
                  <div>Dark Mode "Go": <span className="text-blue-300 bg-gray-800 px-2 py-1 rounded">text-blue-300</span></div>
                  <div>Light Mode "Refund": <span className="text-gray-900 bg-gray-100 px-2 py-1 rounded">text-gray-900</span></div>
                  <div>Light Mode "Go": <span className="text-blue-600 bg-gray-100 px-2 py-1 rounded">text-blue-600</span></div>
                </div>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-3">Current State</h3>
                <div className="space-y-2 text-sm">
                  <div>Theme: <code>{theme}</code></div>
                  <div>isDark: <code>{isDark.toString()}</code></div>
                  <div>HTML class: <code>{typeof window !== 'undefined' ? document.documentElement.className : 'SSR'}</code></div>
                  <div>System Preference: <code>{typeof window !== 'undefined' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : 'SSR'}</code></div>
                </div>
              </div>
            </div>
          </div>

          {/* Test 6: Manual Color Test */}
          <div className="bg-card p-8 rounded-xl border">
            <h2 className="text-2xl font-semibold mb-6 text-card-foreground">Manual Color Test</h2>
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <span className="text-2xl font-bold text-white">Refund</span>
                <span className="text-2xl font-bold text-blue-300">Go</span>
                <span className="text-sm">← Should be visible in dark mode</span>
              </div>
              <div className="flex items-center gap-4">
                <span className="text-2xl font-bold text-gray-900">Refund</span>
                <span className="text-2xl font-bold text-blue-600">Go</span>
                <span className="text-sm">← Should be visible in light mode</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
