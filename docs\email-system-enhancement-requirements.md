# RefundGo Email System Enhancement Requirements

## Document Overview

**Version:** 1.0  
**Date:** 2025-01-29  
**Status:** Draft  
**Priority:** High  

This document specifies implementation requirements for enhancing the RefundGo transactional email system based on comprehensive system analysis. The enhancements address critical gaps in email delivery, consistency, and internationalization.

## Current System Analysis

### Identified Issues

1. **Missing Email Triggers**: Deposit success/failure emails are not sent automatically during payment processing
2. **Inconsistent Branding**: Mixed styling approaches across email templates
3. **Incomplete i18n**: Legacy Chinese-only templates coexist with i18n versions
4. **No Language Detection**: No systematic approach to determine user's preferred email language
5. **Limited Monitoring**: No email delivery tracking or error logging

### System Architecture

- **Email Service**: Resend API (`<EMAIL>`)
- **Template System**: 24 template files in `/src/lib/email-templates/`
- **Email Types**: 15 distinct transactional email types
- **Languages**: Chinese (zh) and English (en) support
- **Integration**: Manual API calls vs. automated webhook triggers

## 1. Financial Transaction Email Integration

### 1.1 Objective

Integrate automatic email notifications into the payment processing workflow to ensure users receive deposit confirmation emails immediately upon payment completion.

### 1.2 Technical Requirements

#### 1.2.1 Webhook Handler Integration

**File**: `/src/app/api/payments/notify/[provider]/route.ts`

**Implementation Location**: After line 282 in the deposit processing block

**Required Changes**:

```typescript
// Add import at top of file
import { notifyDepositSuccess, notifyDepositFailure } from '@/lib/financial-email-integration';

// Add after existing wallet transaction update (line 282)
if (validOrderNo.startsWith('DEPOSIT_') && order.userId) {
  // ... existing balance/transaction updates ...

  // Email notification integration
  try {
    const user = await prisma.user.findUnique({
      where: { id: order.userId },
      select: {
        id: true,
        name: true,
        email: true,
        registrationLanguage: true,
        balance: true,
      },
    });

    if (user?.email) {
      const emailResult = paymentStatus === 'PAID' 
        ? await notifyDepositSuccess(user, depositResult)
        : await notifyDepositFailure(user, depositResult);
      
      // Log email delivery status
      await logEmailDelivery(validOrderNo, user.email, emailResult);
    }
  } catch (emailError) {
    console.error(`Email notification failed for order ${validOrderNo}:`, emailError);
    // Continue webhook processing - don't fail due to email issues
  }
}
```

#### 1.2.2 Email Logging System

**File**: `/src/lib/email-logging.ts` (new file)

**Requirements**:

- Create database table `EmailLog` with fields: id, orderNo, recipientEmail, emailType, status, sentAt, error
- Implement `logEmailDelivery()` function
- Track success/failure rates for monitoring

#### 1.2.3 Error Handling Strategy

- Email failures MUST NOT break webhook processing
- Log all email errors with order reference
- Implement retry mechanism with exponential backoff
- Provide manual email resend capability for failed notifications

### 1.3 Acceptance Criteria

- [ ] Deposit success emails sent automatically when payment status = 'PAID'
- [ ] Deposit failure emails sent automatically when payment status = 'FAILED'
- [ ] Email failures don't interrupt payment processing
- [ ] All email attempts are logged with status and error details
- [ ] Manual testing confirms emails are received within 30 seconds of payment completion

## 2. Email Design Consistency and Branding

### 2.1 Objective

Standardize visual design and branding across all transactional emails to ensure professional, consistent user experience.

### 2.2 Technical Requirements

#### 2.2.1 Unified CSS System

**File**: `/src/hooks/useEmailTranslation.ts`

**Current State**: `emailStyles` constant exists but not used consistently

**Requirements**:

- All templates MUST use `${emailStyles}` CSS injection
- Remove inline styles from individual templates
- Standardize color palette: Primary (#3b82f6), Success (#10b981), Error (#ef4444)
- Ensure mobile-responsive design (min-width: 320px)

#### 2.2.2 Brand Consistency

**Sender Address**: `RefundGo <<EMAIL>>` (consistent across all emails)

**Header Requirements**:

- RefundGo logo/brand name prominently displayed
- Consistent header background and typography
- Professional email client compatibility

**Footer Requirements**:

- Copyright notice: "© 2024 RefundGo. All rights reserved."
- Contact information and support links
- Unsubscribe mechanism (future requirement)

#### 2.2.3 Template Structure Standardization

**Required Structure**:

```html
<!DOCTYPE html>
<html lang="${langAttr}">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${emailTitle}</title>
  <style>${emailStyles}</style>
</head>
<body>
  <div class="email-container">
    <div class="email-header"><!-- Brand header --></div>
    <div class="email-content"><!-- Main content --></div>
    <div class="email-footer"><!-- Standard footer --></div>
  </div>
</body>
</html>
```

### 2.3 Acceptance Criteria

- [ ] All 24 email templates use unified CSS system
- [ ] Consistent RefundGo branding across all email types
- [ ] Mobile-responsive design tested on major email clients
- [ ] Standard sender address used for all transactional emails
- [ ] Professional footer with contact information and copyright

## 3. Comprehensive i18n Support

### 3.1 Objective

Complete internationalization implementation to support both Chinese and English users with proper locale-specific formatting.

### 3.2 Technical Requirements

#### 3.2.1 Template Migration

**Legacy Templates to Convert**:

- `/src/lib/email-templates/notification.ts` → `notification-i18n.ts`
- `/src/lib/email-templates/verification-code.ts` → Already has i18n version
- `/src/lib/email-templates/task-completed-publisher.ts` → `task-completed-publisher-i18n.ts`
- `/src/lib/email-templates/task-completed-accepter.ts` → `task-completed-accepter-i18n.ts`

**Migration Requirements**:

- Extract hardcoded Chinese text to translation objects
- Implement English translations for all text content
- Maintain backward compatibility during transition
- Update import statements in `/src/lib/email.ts`

#### 3.2.2 Centralized Translation Management

**File**: `/src/lib/email-translations.ts` (new file)

**Structure**:

```typescript
export const emailTranslations = {
  zh: {
    common: {
      brandName: 'RefundGo',
      greeting: '您好',
      regards: '此致敬礼',
      team: 'RefundGo 团队',
      // ... more common translations
    },
    notifications: {
      deposit: {
        success: {
          title: '充值成功确认',
          greeting: '您的充值已成功完成！',
          // ... more deposit translations
        }
      }
    }
  },
  en: {
    // English translations
  }
};
```

#### 3.2.3 Locale-Specific Formatting

**Requirements**:

- Currency formatting: `formatEmailAmount(amount, currency, language)`
- Date formatting: `formatEmailDateTime(date, language)`
- Number formatting with proper separators
- Transaction ID masking for security

### 3.3 Acceptance Criteria

- [ ] All 15 email types support both Chinese and English
- [ ] Centralized translation management system implemented
- [ ] Locale-specific formatting for currencies, dates, and numbers
- [ ] Legacy templates successfully migrated to i18n versions
- [ ] Translation consistency verified across all email types

## 4. Intelligent Language Detection

### 4.1 Objective

Implement systematic language detection to ensure users receive emails in their preferred language based on multiple data sources.

### 4.2 Technical Requirements

#### 4.2.1 Language Detection Hierarchy

**Priority Order**:

1. **Primary**: User's `registrationLanguage` field from database
2. **Secondary**: `Accept-Language` header from HTTP request
3. **Tertiary**: URL locale parameter (`/zh/` or `/en/` routes)
4. **Default**: Chinese (zh) if no language can be determined

#### 4.2.2 Utility Function Implementation

**File**: `/src/lib/email-language-detection.ts` (new file)

**Function Signature**:

```typescript
export async function getUserEmailLanguage(
  userId?: string,
  request?: NextRequest,
  fallbackLanguage: 'zh' | 'en' = 'zh'
): Promise<'zh' | 'en'>
```

**Implementation Requirements**:

```typescript
export async function getUserEmailLanguage(
  userId?: string,
  request?: NextRequest,
  fallbackLanguage: 'zh' | 'en' = 'zh'
): Promise<'zh' | 'en'> {
  // 1. Check user's registration language (highest priority)
  if (userId) {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { registrationLanguage: true },
      });

      if (user?.registrationLanguage && ['zh', 'en'].includes(user.registrationLanguage)) {
        return user.registrationLanguage as 'zh' | 'en';
      }
    } catch (error) {
      console.warn('Failed to fetch user language preference:', error);
    }
  }

  // 2. Check Accept-Language header
  if (request) {
    const acceptLanguage = request.headers.get('accept-language');
    if (acceptLanguage?.includes('en')) return 'en';
    if (acceptLanguage?.includes('zh')) return 'zh';
  }

  // 3. Check URL locale (if available in request)
  if (request) {
    const url = new URL(request.url);
    if (url.pathname.startsWith('/en/')) return 'en';
    if (url.pathname.startsWith('/zh/')) return 'zh';
  }

  // 4. Return fallback language
  return fallbackLanguage;
}
```

#### 4.2.3 Integration Points

**Files to Update**:

- `/src/lib/email.ts`: Update all email sending functions to use language detection
- `/src/app/api/send-email/route.ts`: Integrate language detection in API endpoint
- `/src/app/api/payments/notify/[provider]/route.ts`: Use for webhook emails

**Example Integration**:

```typescript
// In email sending functions
const language = await getUserEmailLanguage(userId, request);
const emailData = { ...data, language };
```

### 4.3 Acceptance Criteria

- [ ] Language detection utility function implemented and tested
- [ ] All email sending functions use systematic language detection
- [ ] User's registration language takes highest priority
- [ ] Fallback mechanisms work correctly when primary data unavailable
- [ ] Language detection documented with examples

## 5. Implementation Specifications

### 5.1 Development Phases

#### Phase 1: Critical Email Integration (Week 1)

**Priority**: High

- Implement webhook email triggers for deposit notifications
- Add basic error handling and logging
- Test with payment flow

#### Phase 2: Design Consistency (Week 2)

**Priority**: Medium

- Standardize CSS styling across all templates
- Implement unified branding
- Ensure mobile responsiveness

#### Phase 3: i18n Completion (Week 3)

**Priority**: Medium

- Migrate remaining legacy templates
- Implement centralized translations
- Add locale-specific formatting

#### Phase 4: Language Detection (Week 4)

**Priority**: Low

- Implement intelligent language detection
- Integrate across all email sending points
- Add comprehensive testing

### 5.2 File Modifications Required

#### New Files to Create

- `/src/lib/email-logging.ts` - Email delivery logging system
- `/src/lib/email-language-detection.ts` - Language detection utility
- `/src/lib/email-translations.ts` - Centralized translation management
- `/docs/email-system-testing-guide.md` - Testing procedures

#### Existing Files to Modify

- `/src/app/api/payments/notify/[provider]/route.ts` - Add email triggers
- `/src/lib/email.ts` - Integrate language detection
- `/src/hooks/useEmailTranslation.ts` - Enhance CSS system
- `/src/lib/email-templates/*.ts` - Migrate to i18n versions
- `/prisma/schema.prisma` - Add EmailLog model

### 5.3 Database Schema Changes

#### New EmailLog Model

```prisma
model EmailLog {
  id            String   @id @default(cuid())
  orderNo       String?
  recipientEmail String
  emailType     String
  status        String   // 'SUCCESS', 'FAILED', 'RETRY'
  sentAt        DateTime @default(now())
  error         String?
  retryCount    Int      @default(0)

  @@map("email_logs")
}
```

### 5.4 Testing Requirements

#### 5.4.1 Unit Testing

- Email template rendering with different languages
- Language detection function with various inputs
- Email logging functionality

#### 5.4.2 Integration Testing

- End-to-end deposit flow with email verification
- Webhook processing with email delivery
- Email delivery across different email providers

#### 5.4.3 Manual Testing Checklist

- [ ] Deposit success email received within 30 seconds
- [ ] Deposit failure email received for failed payments
- [ ] Emails display correctly on mobile devices
- [ ] Chinese and English versions render properly
- [ ] Email links and buttons function correctly

### 5.5 Monitoring and Alerting

#### 5.5.1 Email Delivery Metrics

- Success rate percentage (target: >95%)
- Average delivery time (target: <30 seconds)
- Failed email count and reasons
- Retry attempt statistics

#### 5.5.2 Alerting Thresholds

- Email success rate drops below 90%
- More than 10 failed emails in 1 hour
- Email service API errors

### 5.6 Documentation Requirements

#### 5.6.1 Technical Documentation

- Email system architecture overview
- Language detection strategy documentation
- Email template development guidelines
- Troubleshooting guide for email issues

#### 5.6.2 User Documentation

- Email preferences and language settings
- Expected email types and timing
- Contact information for email issues

## 6. Acceptance Criteria Summary

### 6.1 Functional Requirements

- [ ] All deposit transactions trigger appropriate email notifications
- [ ] Email failures don't interrupt payment processing
- [ ] Users receive emails in their preferred language
- [ ] All email templates use consistent branding and styling
- [ ] Mobile-responsive design across all email clients

### 6.2 Technical Requirements

- [ ] Email delivery logging and monitoring implemented
- [ ] Language detection system working across all email types
- [ ] Centralized translation management system
- [ ] Error handling with retry mechanisms
- [ ] Database schema updated with email logging

### 6.3 Quality Requirements

- [ ] Email delivery success rate >95%
- [ ] Email delivery time <30 seconds
- [ ] All email templates pass accessibility standards
- [ ] Comprehensive test coverage for email functionality
- [ ] Documentation complete and up-to-date

## 7. Risk Assessment and Mitigation

### 7.1 Technical Risks

**Risk**: Email service (Resend) API failures
**Mitigation**: Implement retry logic and fallback email service

**Risk**: Database performance impact from email logging
**Mitigation**: Implement async logging and database indexing

**Risk**: Webhook processing delays due to email sending
**Mitigation**: Implement async email sending with queue system

### 7.2 Business Risks

**Risk**: Users not receiving critical financial notifications
**Mitigation**: Implement monitoring and alerting for email failures

**Risk**: Inconsistent user experience across languages
**Mitigation**: Comprehensive testing and translation review process

## 8. Success Metrics

### 8.1 Key Performance Indicators

- Email delivery success rate: >95%
- User complaint reduction: >50%
- Email-related support tickets: <5 per month
- Language detection accuracy: >98%

### 8.2 User Experience Metrics

- Email open rates (baseline measurement)
- User engagement with email content
- Feedback on email design and content quality

---

**Document Prepared By**: System Analysis Team
**Review Required By**: Development Team, Product Manager, QA Team
**Implementation Timeline**: 4 weeks
**Next Review Date**: 2025-02-05
