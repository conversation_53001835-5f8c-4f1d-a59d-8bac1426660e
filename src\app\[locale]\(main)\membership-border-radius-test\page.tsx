'use client';

import { MembershipSection } from '@/components/membership-section';
import { Check, Star } from 'lucide-react';
import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';

export default function MembershipBorderRadiusTestPage() {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  const isDark = theme === 'dark';

  // Mock plan data for testing
  const testPlan = {
    id: "professional",
    badge: "Most Popular",
    title: "Professional",
    price: "$29",
    period: "/month",
    features: [
      "30 task posts per month",
      "Basic tasks",
      "Platform fee 1%",
      "3 whitelist recommendation slots",
      "Priority customer support"
    ],
    buttonText: "Choose Plan",
    popular: true,
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header with theme info */}
      <div className="bg-card border-b p-4">
        <div className="max-w-6xl mx-auto flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-foreground">Membership Card Border Radius Test</h1>
            <p className="text-muted-foreground">
              Current theme: <strong>{theme}</strong> | Testing border radius consistency in membership cards
            </p>
          </div>
          <button
            type="button"
            onClick={() => setTheme(isDark ? 'light' : 'dark')}
            className="bg-primary text-primary-foreground px-4 py-2 rounded-lg"
          >
            Switch to {isDark ? 'Light' : 'Dark'} Mode
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="py-12">
        <div className="max-w-6xl mx-auto px-4">
          <div className="grid gap-8">
            {/* Test 1: Before vs After Comparison */}
            <div className="bg-card p-8 rounded-xl border">
              <h2 className="text-2xl font-semibold mb-6 text-card-foreground">Before vs After Comparison</h2>
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-medium mb-4 text-red-600">Before Fix (Sharp Inner Corners)</h3>
                  <Card className="relative hover:shadow-xl transition-all duration-300 ring-2 ring-blue-600 scale-105">
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <Badge className="bg-blue-600 px-4 py-1">
                        <Star className="w-3 h-3 mr-1" />
                        Most Popular
                      </Badge>
                    </div>

                    {/* Sharp corners version */}
                    <CardHeader className="text-center pb-4 bg-card dark:bg-card">
                      <h3 className="text-2xl font-bold text-foreground mb-2">Professional</h3>
                      <div className="flex items-baseline justify-center">
                        <span className="text-4xl font-bold text-foreground">$29</span>
                        <span className="text-muted-foreground ml-1">/month</span>
                      </div>
                    </CardHeader>

                    <CardContent className="pt-4 bg-card dark:bg-card">
                      <ul className="space-y-4 mb-8">
                        {testPlan.features.map((feature, index) => (
                          <li key={index} className="flex items-start">
                            <Check className="w-5 h-5 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
                            <span className="text-muted-foreground">{feature}</span>
                          </li>
                        ))}
                      </ul>
                      <Button className="w-full bg-blue-600 hover:bg-blue-700" size="lg">
                        Choose Plan
                      </Button>
                    </CardContent>
                  </Card>
                  <p className="text-sm text-red-600 mt-2">
                    ❌ Inner elements have sharp corners that don't match the card's rounded design
                  </p>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium mb-4 text-green-600">After Fix (Rounded Inner Corners)</h3>
                  <Card className="relative hover:shadow-xl transition-all duration-300 ring-2 ring-blue-600 scale-105">
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <Badge className="bg-blue-600 px-4 py-1 rounded-full">
                        <Star className="w-3 h-3 mr-1" />
                        Most Popular
                      </Badge>
                    </div>

                    {/* Fixed version with rounded corners */}
                    <CardHeader className="text-center pb-4 bg-card dark:bg-card rounded-t-xl">
                      <h3 className="text-2xl font-bold text-foreground mb-2">Professional</h3>
                      <div className="flex items-baseline justify-center">
                        <span className="text-4xl font-bold text-foreground">$29</span>
                        <span className="text-muted-foreground ml-1">/month</span>
                      </div>
                    </CardHeader>

                    <CardContent className="pt-4 bg-card dark:bg-card rounded-b-xl">
                      <div className="bg-card/50 dark:bg-card/50 rounded-lg p-4 mb-6">
                        <ul className="space-y-4">
                          {testPlan.features.map((feature, index) => (
                            <li key={index} className="flex items-start">
                              <Check className="w-5 h-5 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
                              <span className="text-muted-foreground">{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                      <Button className="w-full rounded-lg bg-blue-600 hover:bg-blue-700" size="lg">
                        Choose Plan
                      </Button>
                    </CardContent>
                  </Card>
                  <p className="text-sm text-green-600 mt-2">
                    ✅ All inner elements have proper rounded corners matching the card design
                  </p>
                </div>
              </div>
            </div>

            {/* Test 2: Border Radius Consistency Check */}
            <div className="bg-card p-8 rounded-xl border">
              <h2 className="text-2xl font-semibold mb-6 text-card-foreground">Border Radius Consistency Check</h2>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-3">Visual Inspection Points</h3>
                  <div className="grid md:grid-cols-2 gap-4 text-sm">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span>Card outer container: <code>rounded-xl</code></span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span>Card header: <code>rounded-t-xl</code></span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span>Card content: <code>rounded-b-xl</code></span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span>Features container: <code>rounded-lg</code></span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span>Action button: <code>rounded-lg</code></span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span>Popular badge: <code>rounded-full</code></span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Test 3: Theme Consistency Test */}
            <div className="bg-card p-8 rounded-xl border">
              <h2 className="text-2xl font-semibold mb-6 text-card-foreground">Theme Consistency Test</h2>
              <p className="text-muted-foreground mb-6">
                Testing border radius consistency across light and dark themes
              </p>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium mb-3">Light Theme Simulation</h3>
                  <div className="bg-white p-4 rounded-lg border">
                    <Card className="relative hover:shadow-xl transition-all duration-300">
                      <CardHeader className="text-center pb-4 bg-white rounded-t-xl">
                        <h3 className="text-2xl font-bold text-gray-900 mb-2">Test Plan</h3>
                        <div className="flex items-baseline justify-center">
                          <span className="text-4xl font-bold text-gray-900">$29</span>
                          <span className="text-gray-600 ml-1">/month</span>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-4 bg-white rounded-b-xl">
                        <div className="bg-gray-50 rounded-lg p-4 mb-6">
                          <ul className="space-y-2">
                            <li className="flex items-start">
                              <Check className="w-4 h-4 text-green-600 mr-2 mt-0.5" />
                              <span className="text-gray-600 text-sm">Feature 1</span>
                            </li>
                            <li className="flex items-start">
                              <Check className="w-4 h-4 text-green-600 mr-2 mt-0.5" />
                              <span className="text-gray-600 text-sm">Feature 2</span>
                            </li>
                          </ul>
                        </div>
                        <Button className="w-full rounded-lg bg-blue-600 hover:bg-blue-700 text-white" size="sm">
                          Choose Plan
                        </Button>
                      </CardContent>
                    </Card>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium mb-3">Dark Theme Simulation</h3>
                  <div className="bg-gray-900 p-4 rounded-lg border border-gray-700">
                    <Card className="relative hover:shadow-xl transition-all duration-300 bg-gray-800 border-gray-700">
                      <CardHeader className="text-center pb-4 bg-gray-800 rounded-t-xl">
                        <h3 className="text-2xl font-bold text-white mb-2">Test Plan</h3>
                        <div className="flex items-baseline justify-center">
                          <span className="text-4xl font-bold text-white">$29</span>
                          <span className="text-gray-300 ml-1">/month</span>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-4 bg-gray-800 rounded-b-xl">
                        <div className="bg-gray-700/50 rounded-lg p-4 mb-6">
                          <ul className="space-y-2">
                            <li className="flex items-start">
                              <Check className="w-4 h-4 text-green-400 mr-2 mt-0.5" />
                              <span className="text-gray-300 text-sm">Feature 1</span>
                            </li>
                            <li className="flex items-start">
                              <Check className="w-4 h-4 text-green-400 mr-2 mt-0.5" />
                              <span className="text-gray-300 text-sm">Feature 2</span>
                            </li>
                          </ul>
                        </div>
                        <Button className="w-full rounded-lg bg-blue-600 hover:bg-blue-700 text-white" size="sm">
                          Choose Plan
                        </Button>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>
            </div>

            {/* Test 4: Responsive Design Test */}
            <div className="bg-card p-8 rounded-xl border">
              <h2 className="text-2xl font-semibold mb-6 text-card-foreground">Responsive Design Test</h2>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-3">Mobile Simulation (320px width)</h3>
                  <div className="max-w-xs mx-auto">
                    <Card className="relative hover:shadow-xl transition-all duration-300">
                      <CardHeader className="text-center pb-4 bg-card dark:bg-card rounded-t-xl">
                        <h3 className="text-xl font-bold text-foreground mb-2">Pro</h3>
                        <div className="flex items-baseline justify-center">
                          <span className="text-2xl font-bold text-foreground">$29</span>
                          <span className="text-muted-foreground ml-1 text-sm">/mo</span>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-4 bg-card dark:bg-card rounded-b-xl">
                        <div className="bg-card/50 dark:bg-card/50 rounded-lg p-3 mb-4">
                          <ul className="space-y-2">
                            <li className="flex items-start">
                              <Check className="w-4 h-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                              <span className="text-muted-foreground text-sm">30 tasks/month</span>
                            </li>
                            <li className="flex items-start">
                              <Check className="w-4 h-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                              <span className="text-muted-foreground text-sm">Priority support</span>
                            </li>
                          </ul>
                        </div>
                        <Button className="w-full rounded-lg bg-blue-600 hover:bg-blue-700" size="sm">
                          Choose
                        </Button>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Actual Membership Section for Real-World Testing */}
      <div className="mt-12">
        <h2 className="text-2xl font-bold text-center mb-8 text-foreground">Actual Membership Section (Real-World Test)</h2>
        <MembershipSection />
      </div>
    </div>
  );
}
