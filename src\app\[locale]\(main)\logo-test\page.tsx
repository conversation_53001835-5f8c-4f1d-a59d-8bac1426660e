'use client';

import { RefundGoLogoHomepage } from '@/components/refund-go-logo';

export default function LogoTestPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">首页Logo测试</h1>
          <p className="text-lg text-gray-600">
            测试#4 Gradient Animation Version在首页的实现效果
          </p>
        </div>

        <div className="space-y-8">
          {/* 首页Logo版本 */}
          <div className="bg-white p-8 rounded-xl shadow-lg">
            <h2 className="text-2xl font-semibold mb-6 text-gray-800">首页Logo版本 (Gradient Animation Version #4)</h2>
            <p className="text-gray-600 mb-6">
              这是专门为首页设计的#4 Gradient Animation Version，包含完整的动画效果和标语。
            </p>
            <div className="flex justify-center">
              <RefundGoLogoHomepage />
            </div>
          </div>

          {/* 动画效果说明 */}
          <div className="bg-blue-50 p-8 rounded-xl border border-blue-200">
            <h2 className="text-2xl font-semibold mb-6 text-blue-900">动画效果说明</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium mb-3 text-blue-800">鼠标悬停效果</h3>
                <ul className="space-y-2 text-blue-700">
                  <li>• 渐变扫描动画 (1000ms)</li>
                  <li>• 图标旋转 (-45度)</li>
                  <li>• 绿色圆点缩放 (1.5倍)</li>
                  <li>• 文字颜色渐变变化</li>
                  <li>• 阴影增强效果</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-3 text-blue-800">性能优化</h3>
                <ul className="space-y-2 text-blue-700">
                  <li>• GPU加速动画</li>
                  <li>• 减少动画支持</li>
                  <li>• 高对比度模式</li>
                  <li>• 键盘导航支持</li>
                  <li>• 打印样式优化</li>
                </ul>
              </div>
            </div>
          </div>

          {/* 使用说明 */}
          <div className="bg-green-50 p-8 rounded-xl border border-green-200">
            <h2 className="text-2xl font-semibold mb-6 text-green-900">使用说明</h2>
            <div className="space-y-4 text-green-800">
              <p>
                <strong>首页导航栏：</strong> 使用 <code className="bg-green-100 px-2 py-1 rounded">RefundGoLogoHomepage</code> 组件
              </p>
              <p>
                <strong>动画效果：</strong> 包含完整的渐变扫描、图标旋转、颜色变化等动画
              </p>
              <p>
                <strong>响应式设计：</strong> 在不同屏幕尺寸下保持良好的显示效果
              </p>
              <p>
                <strong>无障碍支持：</strong> 支持键盘导航、屏幕阅读器、减少动画等功能
              </p>
            </div>
          </div>

          {/* 技术实现 */}
          <div className="bg-gray-100 p-8 rounded-xl">
            <h2 className="text-2xl font-semibold mb-6 text-gray-900">技术实现</h2>
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-medium mb-2 text-gray-800">组件结构</h3>
                <pre className="bg-gray-800 text-green-400 p-4 rounded-lg text-sm overflow-x-auto">
{`<RefundGoLogoHomepage 
  className="bg-transparent shadow-none hover:shadow-xl" 
/>`}
                </pre>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-2 text-gray-800">核心特性</h3>
                <ul className="space-y-1 text-gray-700">
                  <li>• 基于原始#4 Gradient Animation Version设计</li>
                  <li>• 使用Tailwind CSS和Lucide React图标</li>
                  <li>• 支持TypeScript类型检查</li>
                  <li>• 包含完整的CSS动画优化</li>
                  <li>• 遵循无障碍设计标准</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
