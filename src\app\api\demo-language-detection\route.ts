/**
 * 演示语言检测机制
 * 展示系统如何判断用户在 /zh/sign-in 还是 /en/sign-in
 */

import { NextRequest, NextResponse } from 'next/server';

import { getUserEmailLanguage } from '@/lib/email-language-detection';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { simulatePageUrl } = body;

    // 创建模拟请求，设置 Referer 头部
    const mockHeaders = new Headers();
    mockHeaders.set('referer', simulatePageUrl);
    mockHeaders.set('accept-language', 'en-US,en;q=0.9,zh-CN,zh;q=0.8'); // 模拟浏览器语言

    const mockRequest = {
      ...request,
      headers: mockHeaders,
      url: request.url,
    } as NextRequest;

    console.log('\n🔍 开始语言检测演示...');
    console.log(`📍 模拟页面URL: ${simulatePageUrl}`);

    // 执行语言检测
    const detectedLanguage = await getUserEmailLanguage(
      'demo-user-123', // 模拟用户ID
      mockRequest,
      'zh'
    );

    // 手动解析URL来展示检测过程
    const urlAnalysis = analyzeUrl(simulatePageUrl);

    return NextResponse.json({
      success: true,
      demonstration: {
        currentPageUrl: simulatePageUrl,
        detectionProcess: {
          step1: {
            description: '检查用户数据库语言偏好',
            result: '无设置（跳过）',
            priority: 1,
          },
          step2: {
            description: '检查URL路径中的语言标识',
            refererHeader: simulatePageUrl,
            urlAnalysis: urlAnalysis,
            detectedFromUrl: urlAnalysis.detectedLanguage,
            priority: 2,
            isUsed: !!urlAnalysis.detectedLanguage,
          },
          step3: {
            description: '检查浏览器Accept-Language头部',
            acceptLanguageHeader: 'en-US,en;q=0.9,zh-CN,zh;q=0.8',
            wouldDetect: 'en',
            priority: 3,
            isUsed: !urlAnalysis.detectedLanguage,
          },
          step4: {
            description: '默认回退语言',
            fallback: 'zh',
            priority: 4,
            isUsed: !urlAnalysis.detectedLanguage && !mockHeaders.get('accept-language'),
          },
        },
        finalResult: {
          detectedLanguage,
          usedMethod: urlAnalysis.detectedLanguage ? 'URL路径检测' : 'Accept-Language头部',
          confidence: urlAnalysis.detectedLanguage ? '高（用户主动选择）' : '中（浏览器默认）',
        },
      },
      examples: {
        chineseSignIn: {
          url: 'https://refundgo.org/zh/sign-in',
          expectedResult: 'zh',
          reason: 'URL路径第一段是 "zh"',
        },
        englishSignIn: {
          url: 'https://refundgo.org/en/sign-in',
          expectedResult: 'en', 
          reason: 'URL路径第一段是 "en"',
        },
        noLocaleUrl: {
          url: 'https://refundgo.org/sign-in',
          expectedResult: 'en',
          reason: '无URL语言标识，使用Accept-Language头部',
        },
      },
    });

  } catch (error) {
    console.error('语言检测演示错误:', error);
    return NextResponse.json(
      { 
        error: '演示失败',
        details: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 },
    );
  }
}

// 辅助函数：分析URL结构
function analyzeUrl(url: string) {
  try {
    const urlObj = new URL(url);
    const pathSegments = urlObj.pathname.split('/').filter(Boolean);
    
    let detectedLanguage = null;
    if (pathSegments.length > 0) {
      const firstSegment = pathSegments[0].toLowerCase();
      if (firstSegment === 'zh') {
        detectedLanguage = 'zh';
      } else if (firstSegment === 'en') {
        detectedLanguage = 'en';
      }
    }

    return {
      fullUrl: url,
      pathname: urlObj.pathname,
      pathSegments: pathSegments,
      firstSegment: pathSegments[0] || null,
      detectedLanguage,
      analysis: detectedLanguage 
        ? `✅ 检测到语言: ${detectedLanguage}`
        : '❌ 未检测到语言标识',
    };
  } catch (error) {
    return {
      fullUrl: url,
      error: '无效的URL格式',
      detectedLanguage: null,
    };
  }
}

export async function GET() {
  return NextResponse.json({
    message: '语言检测机制演示',
    description: '展示系统如何判断用户在 /zh/sign-in 还是 /en/sign-in',
    detectionHierarchy: {
      tier1: '用户数据库语言偏好 (registrationLanguage)',
      tier2: 'URL路径语言标识 (/zh/ 或 /en/) - 优先级最高',
      tier3: '浏览器Accept-Language头部 - 浏览器默认',
      tier4: '默认回退到中文 (zh)',
    },
    usage: {
      method: 'POST',
      body: {
        simulatePageUrl: 'string (必需，要模拟的页面URL)',
      },
    },
    examples: {
      chineseSignIn: {
        simulatePageUrl: 'https://refundgo.org/zh/sign-in',
      },
      englishSignIn: {
        simulatePageUrl: 'https://refundgo.org/en/sign-in',
      },
      chineseSignUp: {
        simulatePageUrl: 'https://refundgo.org/zh/sign-up',
      },
      englishSignUp: {
        simulatePageUrl: 'https://refundgo.org/en/sign-up',
      },
    },
    keyPoints: [
      'Referer头部包含用户当前页面的完整URL',
      'URL路径的第一段用于识别语言 (/zh/ 或 /en/)',
      'URL语言检测优先级高于浏览器语言设置',
      '这确保了邮件语言与当前页面语言一致',
    ],
  });
}
