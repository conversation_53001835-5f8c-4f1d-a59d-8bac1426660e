/**
 * 真实邮件调试测试端点
 * 模拟真实的前端请求，触发完整的调试日志
 */

import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      simulatePageUrl,
      action = 'send-current-email-code',
      testEmail,
      acceptLanguage = 'en-US,en;q=0.9,zh-CN,zh;q=0.8'
    } = body;

    console.log('\n🧪 开始真实邮件调试测试...');
    console.log(`📍 模拟页面: ${simulatePageUrl}`);
    console.log(`🎯 测试操作: ${action}`);

    // 创建模拟请求头部
    const testHeaders = new Headers();
    testHeaders.set('content-type', 'application/json');
    testHeaders.set('referer', simulatePageUrl);
    testHeaders.set('accept-language', acceptLanguage);

    // 准备请求数据
    let requestBody: any = { action };
    if (action === 'send-new-email-code' && testEmail) {
      requestBody.email = testEmail;
    }

    console.log(`📦 请求数据:`, requestBody);
    console.log(`📋 请求头部:`, {
      'content-type': testHeaders.get('content-type'),
      'referer': testHeaders.get('referer'),
      'accept-language': testHeaders.get('accept-language'),
    });

    // 发送请求到真实的邮件API
    const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/user/email`, {
      method: 'POST',
      headers: testHeaders,
      body: JSON.stringify(requestBody),
    });

    const responseData = await response.json();

    console.log(`📬 API响应状态: ${response.status}`);
    console.log(`📄 API响应数据:`, responseData);

    return NextResponse.json({
      success: true,
      message: '真实邮件调试测试完成',
      testInfo: {
        simulatedPageUrl: simulatePageUrl,
        action: action,
        testEmail: testEmail,
        acceptLanguage: acceptLanguage,
      },
      apiResponse: {
        status: response.status,
        data: responseData,
      },
      debugInstructions: [
        '1. 查看服务器控制台日志',
        '2. 寻找以 🚀 开头的调试信息',
        '3. 检查语言检测过程',
        '4. 验证邮件主题和内容语言一致性',
      ],
    });

  } catch (error) {
    console.error('❌ 真实邮件调试测试失败:', error);
    return NextResponse.json(
      { 
        error: '测试失败',
        details: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 },
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: '真实邮件调试测试端点',
    description: '模拟真实前端请求，触发完整的调试日志',
    usage: {
      method: 'POST',
      body: {
        simulatePageUrl: 'string (必需，模拟的页面URL)',
        action: 'send-current-email-code | send-new-email-code (可选，默认: send-current-email-code)',
        testEmail: 'string (可选，用于send-new-email-code操作)',
        acceptLanguage: 'string (可选，模拟浏览器语言偏好)',
      },
    },
    examples: {
      chineseSignIn: {
        simulatePageUrl: 'https://refundgo.org/zh/sign-in',
        action: 'send-current-email-code',
        acceptLanguage: 'en-US,en;q=0.9',
      },
      englishSignUp: {
        simulatePageUrl: 'https://refundgo.org/en/sign-up',
        action: 'send-current-email-code',
        acceptLanguage: 'zh-CN,zh;q=0.9',
      },
      newEmailChange: {
        simulatePageUrl: 'https://refundgo.org/zh/settings',
        action: 'send-new-email-code',
        testEmail: '<EMAIL>',
        acceptLanguage: 'en-US,en;q=0.9',
      },
    },
    debugInfo: {
      lookFor: [
        '🚀 开始邮件验证码发送流程...',
        '🔍 开始语言检测...',
        '✅ 最终检测语言',
        '📧 准备发送邮件...',
        '📬 邮件主题',
        '📄 邮件内容预览',
      ],
      consoleLocation: '查看服务器控制台输出',
    },
  });
}
