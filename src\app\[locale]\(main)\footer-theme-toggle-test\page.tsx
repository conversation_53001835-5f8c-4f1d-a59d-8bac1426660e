'use client';

import { Footer } from '@/components/footer';
import { FooterModeToggle } from '@/components/footer-mode-toggle';
import { ModeToggle } from '@/components/mode-toggle';
import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';

export default function FooterThemeToggleTestPage() {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  const isDark = theme === 'dark';

  return (
    <div className="min-h-screen bg-background">
      {/* Header with theme info */}
      <div className="bg-card border-b p-4">
        <div className="max-w-6xl mx-auto flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-foreground">Footer Theme Toggle Test</h1>
            <p className="text-muted-foreground">
              Current theme: <strong>{theme}</strong> | Testing footer theme toggle visibility
            </p>
          </div>
          <button
            type="button"
            onClick={() => setTheme(isDark ? 'light' : 'dark')}
            className="bg-primary text-primary-foreground px-4 py-2 rounded-lg"
          >
            Switch to {isDark ? 'Light' : 'Dark'} Mode
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="py-12">
        <div className="max-w-6xl mx-auto px-4">
          <div className="grid gap-8">
            {/* Test 1: Theme Toggle Comparison */}
            <div className="bg-card p-8 rounded-xl border">
              <h2 className="text-2xl font-semibold mb-6 text-card-foreground">Theme Toggle Comparison</h2>
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-medium mb-4">Original ModeToggle (Problematic)</h3>
                  <div className="bg-gray-900 p-6 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <span className="text-white text-sm">Social Icons</span>
                      <div className="border-l border-gray-700 pl-4">
                        <ModeToggle />
                      </div>
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground mt-2">
                    ❌ In light mode, this appears as a bright white button on dark footer
                  </p>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium mb-4">FooterModeToggle (Fixed)</h3>
                  <div className="bg-gray-900 p-6 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <span className="text-white text-sm">Social Icons</span>
                      <div className="border-l border-gray-700 pl-4">
                        <FooterModeToggle />
                      </div>
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground mt-2">
                    ✅ Properly styled for dark footer background in both themes
                  </p>
                </div>
              </div>
            </div>

            {/* Test 2: Light Mode Visibility Test */}
            <div className="bg-card p-8 rounded-xl border">
              <h2 className="text-2xl font-semibold mb-6 text-card-foreground">Light Mode Visibility Test</h2>
              <p className="text-muted-foreground mb-6">
                The footer theme toggle should be clearly visible and properly styled in light mode
              </p>
              <div className="bg-gray-900 p-8 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="text-white">
                    <h4 className="text-lg font-semibold mb-2">RefundGo</h4>
                    <p className="text-gray-300 text-sm">Professional refund services</p>
                  </div>
                  <div className="flex items-center space-x-4">
                    <span className="text-gray-400 text-sm">Social Links</span>
                    <div className="border-l border-gray-700 pl-4">
                      <FooterModeToggle />
                    </div>
                  </div>
                </div>
              </div>
              <div className="mt-4 space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span>Toggle should have subtle gray background with border</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span>Icons should be gray-400 color, visible against dark background</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span>Hover state should lighten to white with gray-800 background</span>
                </div>
              </div>
            </div>

            {/* Test 3: Dark Mode Consistency Test */}
            <div className="bg-card p-8 rounded-xl border">
              <h2 className="text-2xl font-semibold mb-6 text-card-foreground">Dark Mode Consistency Test</h2>
              <p className="text-muted-foreground mb-6">
                The footer theme toggle should also work well in dark mode
              </p>
              <div className="bg-gray-950 p-8 rounded-lg border border-gray-800">
                <div className="flex items-center justify-between">
                  <div className="text-white">
                    <h4 className="text-lg font-semibold mb-2">RefundGo</h4>
                    <p className="text-gray-300 text-sm">Professional refund services</p>
                  </div>
                  <div className="flex items-center space-x-4">
                    <span className="text-gray-400 text-sm">Social Links</span>
                    <div className="border-l border-gray-500 pl-4">
                      <FooterModeToggle />
                    </div>
                  </div>
                </div>
              </div>
              <p className="text-sm text-muted-foreground mt-4">
                In dark mode, the toggle should have slightly different styling but remain clearly visible
              </p>
            </div>

            {/* Test 4: Dropdown Menu Test */}
            <div className="bg-card p-8 rounded-xl border">
              <h2 className="text-2xl font-semibold mb-6 text-card-foreground">Dropdown Menu Test</h2>
              <p className="text-muted-foreground mb-6">
                Click the theme toggle to test the dropdown menu visibility and styling
              </p>
              <div className="bg-gray-900 p-8 rounded-lg relative">
                <div className="flex items-center justify-center">
                  <FooterModeToggle />
                </div>
                <div className="mt-6 text-center">
                  <p className="text-white text-sm mb-2">Click the toggle above to test:</p>
                  <div className="space-y-1 text-xs text-gray-300">
                    <div>• Dropdown should appear with dark background</div>
                    <div>• Menu items should be clearly visible</div>
                    <div>• Hover states should work properly</div>
                    <div>• Icons should be visible next to each option</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Test 5: Accessibility Test */}
            <div className="bg-card p-8 rounded-xl border">
              <h2 className="text-2xl font-semibold mb-6 text-card-foreground">Accessibility Test</h2>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium mb-3">Keyboard Navigation</h3>
                  <div className="bg-gray-900 p-4 rounded-lg">
                    <FooterModeToggle />
                  </div>
                  <div className="mt-2 space-y-1 text-sm text-muted-foreground">
                    <div>• Tab to focus the toggle button</div>
                    <div>• Enter/Space to open dropdown</div>
                    <div>• Arrow keys to navigate options</div>
                    <div>• Enter to select option</div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium mb-3">Visual Indicators</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span>Focus ring should be visible when focused</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span>Screen reader text for accessibility</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span>High contrast support</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Test 6: Responsive Behavior */}
            <div className="bg-card p-8 rounded-xl border">
              <h2 className="text-2xl font-semibold mb-6 text-card-foreground">Responsive Behavior</h2>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-3">Desktop View</h3>
                  <div className="bg-gray-900 p-6 rounded-lg">
                    <div className="flex items-center justify-between">
                      <span className="text-white">Footer Content</span>
                      <FooterModeToggle />
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium mb-3">Mobile Simulation</h3>
                  <div className="bg-gray-900 p-4 rounded-lg max-w-sm">
                    <div className="flex items-center justify-between">
                      <span className="text-white text-sm">Footer</span>
                      <FooterModeToggle />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Actual Footer for Real-World Testing */}
      <div className="mt-12">
        <h2 className="text-2xl font-bold text-center mb-8 text-foreground">Actual Footer (Real-World Test)</h2>
        <Footer />
      </div>
    </div>
  );
}
