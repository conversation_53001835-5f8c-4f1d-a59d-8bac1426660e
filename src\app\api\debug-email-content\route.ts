/**
 * Debug Email Content to Identify Language Issues
 */

import { NextRequest, NextResponse } from 'next/server';

import { getUserEmailLanguage } from '@/lib/email-language-detection';
import { getEmailTranslations } from '@/lib/email-translations';
import {
  verificationCodeTemplateI18n,
  type VerificationCodeData,
} from '@/lib/email-templates/verification-code-i18n';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { language = 'en' } = body;

    // Create test data
    const emailData: VerificationCodeData = {
      userName: 'Test User',
      userEmail: '<EMAIL>',
      verificationCode: '123456',
      expiresIn: 10,
      action: 'register',
      language: language as 'zh' | 'en',
    };

    // Generate email content
    const emailHtml = verificationCodeTemplateI18n(emailData);
    const translations = getEmailTranslations(language as 'zh' | 'en');

    // Extract key phrases to check language
    const keyPhrases = {
      english: [
        'Registration Verification Code',
        'Thank you for registering',
        'Verification Code:',
        'minutes',
        'Best regards',
        'RefundGo Team',
        'This email was sent automatically',
        'All rights reserved'
      ],
      chinese: [
        '注册验证码',
        '感谢您注册',
        '验证码',
        '分钟',
        '此致敬礼',
        'RefundGo 团队',
        '此邮件由 RefundGo 系统自动发送',
        '保留所有权利'
      ]
    };

    // Check which phrases are present
    const englishMatches = keyPhrases.english.filter(phrase => 
      emailHtml.includes(phrase)
    );
    const chineseMatches = keyPhrases.chinese.filter(phrase => 
      emailHtml.includes(phrase)
    );

    return NextResponse.json({
      success: true,
      testLanguage: language,
      emailSubject: language === 'en' 
        ? translations.notifications.verification.register.subject
        : translations.notifications.verification.register.subject,
      emailContentAnalysis: {
        englishPhrasesFound: englishMatches,
        chinesePhrasesFound: chineseMatches,
        englishCount: englishMatches.length,
        chineseCount: chineseMatches.length,
        predominantLanguage: englishMatches.length > chineseMatches.length ? 'English' : 'Chinese',
        isConsistent: language === 'en' 
          ? englishMatches.length > chineseMatches.length
          : chineseMatches.length > englishMatches.length,
      },
      translationsAvailable: {
        register: translations.notifications.verification.register,
        common: translations.common,
        ui: translations.ui,
      },
      rawEmailHtml: emailHtml.substring(0, 1000) + '...', // First 1000 chars for debugging
    });

  } catch (error) {
    console.error('Debug email content error:', error);
    return NextResponse.json(
      { 
        error: 'Debug failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Debug Email Content Endpoint',
    usage: {
      method: 'POST',
      body: {
        language: 'zh | en (required)',
      },
    },
    examples: {
      english: { language: 'en' },
      chinese: { language: 'zh' },
    },
  });
}
