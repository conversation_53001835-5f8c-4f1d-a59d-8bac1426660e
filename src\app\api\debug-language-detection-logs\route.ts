/**
 * 语言检测调试日志端点
 * 直接测试语言检测函数，查看详细日志
 */

import { NextRequest, NextResponse } from 'next/server';

import { getUserEmailLanguage } from '@/lib/email-language-detection';
import { getEmailTranslations } from '@/lib/email-translations';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      simulatePageUrl,
      simulateUserId = 'debug-user-123',
      simulateAcceptLanguage = 'en-US,en;q=0.9,zh-CN,zh;q=0.8'
    } = body;

    console.log('\n🔥🔥🔥 开始语言检测调试 🔥🔥🔥');
    console.log(`📍 模拟页面URL: ${simulatePageUrl}`);
    console.log(`👤 模拟用户ID: ${simulateUserId}`);
    console.log(`🌍 模拟Accept-Language: ${simulateAcceptLanguage}`);

    // 创建模拟请求
    const mockHeaders = new Headers();
    mockHeaders.set('referer', simulatePageUrl);
    mockHeaders.set('accept-language', simulateAcceptLanguage);

    const mockRequest = {
      ...request,
      headers: mockHeaders,
      url: request.url,
    } as NextRequest;

    console.log('\n📋 模拟请求头部信息:');
    console.log(`   Referer: ${mockHeaders.get('referer')}`);
    console.log(`   Accept-Language: ${mockHeaders.get('accept-language')}`);
    console.log(`   Request URL: ${mockRequest.url}`);

    // 执行语言检测（这里会触发详细的调试日志）
    console.log('\n🎯 调用 getUserEmailLanguage 函数...');
    const detectedLanguage = await getUserEmailLanguage(
      simulateUserId,
      mockRequest,
      'zh'
    );

    console.log(`\n🎉 语言检测完成！最终结果: ${detectedLanguage}`);

    // 获取翻译
    const translations = getEmailTranslations(detectedLanguage);

    // 测试不同的邮件主题
    const emailSubjects = {
      register: translations.notifications.verification.register.subject,
      login: translations.notifications.verification.login.subject,
      verifyEmail: translations.notifications.verification.verifyEmail.subject,
      changeEmail: translations.notifications.verification.changeEmail.subject,
    };

    console.log('\n📬 邮件主题测试:');
    Object.entries(emailSubjects).forEach(([action, subject]) => {
      console.log(`   ${action}: ${subject}`);
    });

    console.log('\n🔥🔥🔥 调试完成 🔥🔥🔥\n');

    return NextResponse.json({
      success: true,
      message: '语言检测调试完成',
      debugResults: {
        simulatedInputs: {
          pageUrl: simulatePageUrl,
          userId: simulateUserId,
          acceptLanguage: simulateAcceptLanguage,
        },
        detectionResult: {
          detectedLanguage,
          languageName: detectedLanguage === 'zh' ? '中文' : '英文',
        },
        emailSubjects,
        commonTranslations: {
          greeting: translations.common.greeting,
          regards: translations.common.regards,
          team: translations.common.team,
          brandName: translations.common.brandName,
        },
      },
      instructions: [
        '🔍 查看服务器控制台输出',
        '📋 寻找详细的语言检测过程',
        '🎯 确认URL路径解析是否正确',
        '✅ 验证最终检测结果',
      ],
    });

  } catch (error) {
    console.error('❌ 语言检测调试失败:', error);
    return NextResponse.json(
      { 
        error: '调试失败',
        details: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 },
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: '语言检测调试日志端点',
    description: '直接测试语言检测函数，查看详细的控制台日志',
    usage: {
      method: 'POST',
      body: {
        simulatePageUrl: 'string (必需，模拟的页面URL)',
        simulateUserId: 'string (可选，模拟用户ID)',
        simulateAcceptLanguage: 'string (可选，模拟Accept-Language头部)',
      },
    },
    testCases: {
      chinesePageEnglishBrowser: {
        description: '中文页面 + 英文浏览器（应该检测到中文）',
        payload: {
          simulatePageUrl: 'https://refundgo.org/zh/sign-in',
          simulateAcceptLanguage: 'en-US,en;q=0.9',
        },
        expectedResult: 'zh',
      },
      englishPageChineseBrowser: {
        description: '英文页面 + 中文浏览器（应该检测到英文）',
        payload: {
          simulatePageUrl: 'https://refundgo.org/en/sign-up',
          simulateAcceptLanguage: 'zh-CN,zh;q=0.9',
        },
        expectedResult: 'en',
      },
      noLocaleUrl: {
        description: '无语言标识URL + 英文浏览器（应该检测到英文）',
        payload: {
          simulatePageUrl: 'https://refundgo.org/dashboard',
          simulateAcceptLanguage: 'en-US,en;q=0.9',
        },
        expectedResult: 'en',
      },
    },
    debugOutput: {
      lookFor: [
        '🔥🔥🔥 开始语言检测调试 🔥🔥🔥',
        '📧 Language detection: Using user preference',
        '📧 Language detection: Using URL locale',
        '📧 Language detection: Using Accept-Language header',
        '📧 Language detection: Using fallback language',
        '🔍 URL Analysis:',
        '✅ Detected English/Chinese locale from URL',
        '🎉 语言检测完成！',
      ],
    },
  });
}
