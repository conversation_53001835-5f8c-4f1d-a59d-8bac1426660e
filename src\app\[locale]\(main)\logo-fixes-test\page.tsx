'use client';

import { RefundGo<PERSON>ogo, RefundGoLogoCompact, RefundGoLogoIcon, RefundGoLogoHomepage } from '@/components/refund-go-logo';
import { useTheme } from 'next-themes';

export default function LogoFixesTestPage() {
  const { theme, setTheme } = useTheme();
  const isDark = theme === 'dark';

  return (
    <div className="min-h-screen bg-background py-12">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-foreground mb-4">Logo Fixes Test Page</h1>
          <p className="text-lg text-muted-foreground mb-6">
            Testing all 5 logo fixes: Footer colors, Dark mode, Background removal, Auth panel, VIP integration
          </p>
          <button
            onClick={() => setTheme(isDark ? 'light' : 'dark')}
            className="bg-primary text-primary-foreground px-4 py-2 rounded-lg"
          >
            Switch to {isDark ? 'Light' : 'Dark'} Mode
          </button>
        </div>

        <div className="grid gap-8">
          {/* Fix 1: Footer Logo Color Test */}
          <div className="bg-black p-8 rounded-xl">
            <h2 className="text-2xl font-semibold mb-6 text-white">Fix 1: Footer Logo (White Text)</h2>
            <p className="text-gray-300 mb-6">
              Logo should display with white text on dark background
            </p>
            <RefundGoLogo 
              variant="static" 
              size="md" 
              animated={false} 
              showTagline={true}
              forceWhiteText={true}
              className="bg-transparent shadow-none"
            />
          </div>

          {/* Fix 2: Dark Mode Compatibility Test */}
          <div className="bg-card p-8 rounded-xl border">
            <h2 className="text-2xl font-semibold mb-6 text-card-foreground">Fix 2: Dark Mode Compatibility</h2>
            <p className="text-muted-foreground mb-6">
              Logo should adapt colors based on current theme: {theme}
            </p>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium mb-3">Homepage Logo</h3>
                <RefundGoLogoHomepage darkMode={isDark} />
              </div>
              <div>
                <h3 className="text-lg font-medium mb-3">Compact Logo</h3>
                <RefundGoLogoCompact animated={true} darkMode={isDark} />
              </div>
            </div>
          </div>

          {/* Fix 3: Background Removal Test */}
          <div className="bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 p-8 rounded-xl">
            <h2 className="text-2xl font-semibold mb-6 text-foreground">Fix 3: Background Removal</h2>
            <p className="text-muted-foreground mb-6">
              Logo should have transparent/minimal background, no visual conflicts
            </p>
            <div className="flex flex-wrap gap-6 items-center">
              <RefundGoLogoHomepage 
                darkMode={isDark}
                className="bg-transparent shadow-none hover:shadow-lg"
              />
              <RefundGoLogo 
                variant="full" 
                size="md" 
                animated={true} 
                showTagline={false}
                darkMode={isDark}
                className="bg-transparent shadow-none hover:shadow-lg"
              />
            </div>
          </div>

          {/* Fix 4: Auth Panel Logo Test */}
          <div className="bg-muted p-8 rounded-xl">
            <h2 className="text-2xl font-semibold mb-6 text-foreground">Fix 4: Auth Panel Logo</h2>
            <p className="text-muted-foreground mb-6">
              Logo should integrate seamlessly with auth panel design
            </p>
            <div className="bg-background p-6 rounded-lg border">
              <RefundGoLogo 
                variant="full" 
                size="md" 
                animated={true} 
                showTagline={true}
                darkMode={isDark}
                className="bg-transparent shadow-none hover:shadow-none"
              />
            </div>
          </div>

          {/* Fix 5: VIP Integration Test */}
          <div className="bg-card p-8 rounded-xl border">
            <h2 className="text-2xl font-semibold mb-6 text-card-foreground">Fix 5: VIP Membership Integration</h2>
            <p className="text-muted-foreground mb-6">
              Logo colors should change based on VIP membership level
            </p>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center">
                <h3 className="text-sm font-medium mb-3 text-muted-foreground">Basic (Free)</h3>
                <RefundGoLogoIcon size="md" vipLevel="basic" darkMode={isDark} />
                <p className="text-xs mt-2 text-muted-foreground">Blue gradient</p>
              </div>
              <div className="text-center">
                <h3 className="text-sm font-medium mb-3 text-muted-foreground">Premium (Pro)</h3>
                <RefundGoLogoIcon size="md" vipLevel="premium" darkMode={isDark} />
                <p className="text-xs mt-2 text-muted-foreground">Purple gradient</p>
              </div>
              <div className="text-center">
                <h3 className="text-sm font-medium mb-3 text-muted-foreground">VIP (Business)</h3>
                <RefundGoLogoIcon size="md" vipLevel="vip" darkMode={isDark} />
                <p className="text-xs mt-2 text-muted-foreground">Gold gradient</p>
              </div>
              <div className="text-center">
                <h3 className="text-sm font-medium mb-3 text-muted-foreground">Enterprise</h3>
                <RefundGoLogoIcon size="md" vipLevel="enterprise" darkMode={isDark} />
                <p className="text-xs mt-2 text-muted-foreground">Gray gradient</p>
              </div>
            </div>
          </div>

          {/* Full Logo Variants with VIP Colors */}
          <div className="bg-card p-8 rounded-xl border">
            <h2 className="text-2xl font-semibold mb-6 text-card-foreground">VIP Full Logo Variants</h2>
            <div className="grid gap-6">
              <div>
                <h3 className="text-lg font-medium mb-3">Basic Member</h3>
                <RefundGoLogo 
                  variant="static" 
                  size="md" 
                  showTagline={true}
                  vipLevel="basic"
                  darkMode={isDark}
                />
              </div>
              <div>
                <h3 className="text-lg font-medium mb-3">Premium Member</h3>
                <RefundGoLogo 
                  variant="static" 
                  size="md" 
                  showTagline={true}
                  vipLevel="premium"
                  darkMode={isDark}
                />
              </div>
              <div>
                <h3 className="text-lg font-medium mb-3">VIP Member</h3>
                <RefundGoLogo 
                  variant="static" 
                  size="md" 
                  showTagline={true}
                  vipLevel="vip"
                  darkMode={isDark}
                />
              </div>
              <div>
                <h3 className="text-lg font-medium mb-3">Enterprise Member</h3>
                <RefundGoLogo 
                  variant="static" 
                  size="md" 
                  showTagline={true}
                  vipLevel="enterprise"
                  darkMode={isDark}
                />
              </div>
            </div>
          </div>

          {/* Accessibility Test */}
          <div className="bg-muted p-8 rounded-xl">
            <h2 className="text-2xl font-semibold mb-6 text-foreground">Accessibility & Performance</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium mb-3">Features Maintained</h3>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>✅ High contrast mode support</li>
                  <li>✅ Reduced motion preferences</li>
                  <li>✅ Keyboard navigation focus</li>
                  <li>✅ Screen reader compatibility</li>
                  <li>✅ Print optimization</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-3">Performance Optimizations</h3>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>✅ GPU acceleration</li>
                  <li>✅ Overflow prevention</li>
                  <li>✅ Responsive design</li>
                  <li>✅ Dark mode support</li>
                  <li>✅ Background transparency</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
