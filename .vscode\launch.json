{"version": "0.2.0", "configurations": [{"name": "Next.js: debug server-side", "type": "node-terminal", "request": "launch", "command": "npm run dev:debug", "skipFiles": ["<node_internals>/**"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"name": "Next.js: debug client-side", "type": "chrome", "request": "launch", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}", "sourceMapPathOverrides": {"webpack://_N_E/*": "${webRoot}/*", "webpack:///./*": "${webRoot}/*", "webpack:///./~/*": "${webRoot}/node_modules/*", "webpack://?:*/*": "${webRoot}/*", "webpack:///src/*": "${webRoot}/src/*", "webpack:///app/*": "${webRoot}/app/*", "webpack:///components/*": "${webRoot}/components/*", "webpack:///lib/*": "${webRoot}/lib/*"}, "userDataDir": false, "runtimeArgs": ["--disable-features=VizDisplayCompositor"]}, {"name": "Next.js: debug client-side (Firefox)", "type": "firefox", "request": "launch", "url": "http://localhost:3000", "reAttach": true, "pathMappings": [{"url": "webpack://_N_E", "path": "${workspaceFolder}"}]}, {"name": "Next.js: debug full stack", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/next/dist/bin/next", "args": ["dev", "--port", "3000"], "runtimeArgs": ["--inspect"], "skipFiles": ["<node_internals>/**"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "serverReadyAction": {"action": "debugWithEdge", "killOnServerStop": true, "pattern": "- Local:.+(https?://.+)", "uriFormat": "%s", "webRoot": "${workspaceFolder}"}}, {"name": "Jest: Debug Current File", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["--runInBand", "--no-coverage", "--no-cache", "${relativeFile}"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "windows": {"program": "${workspaceFolder}/node_modules/jest/bin/jest"}}, {"name": "Jest: Debug All Tests", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["--runInBand", "--no-coverage", "--no-cache"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "windows": {"program": "${workspaceFolder}/node_modules/jest/bin/jest"}}, {"name": "Jest: Debug Watch Mode", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["--runInBand", "--no-coverage", "--no-cache", "--watch<PERSON>ll"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "windows": {"program": "${workspaceFolder}/node_modules/jest/bin/jest"}}, {"name": "TypeScript: Compile and Debug", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/tsc", "args": ["--noEmit"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"name": "ESLint: Debug Current File", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/eslint", "args": ["${relativeFile}", "--debug"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"name": "Playwright: Debug Tests", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/playwright", "args": ["test", "--debug"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "env": {"PWDEBUG": "1"}}, {"name": "Node.js: Attach to Process", "type": "node", "request": "attach", "port": 9229, "restart": true, "localRoot": "${workspaceFolder}", "remoteRoot": "${workspaceFolder}"}], "compounds": [{"name": "Next.js: Launch Full Stack (Recommended)", "configurations": ["Next.js: debug server-side", "Next.js: debug client-side"], "stopAll": true, "presentation": {"hidden": false, "group": "Next.js", "order": 1}}, {"name": "Next.js: Launch with Firefox", "configurations": ["Next.js: debug server-side", "Next.js: debug client-side (Firefox)"], "stopAll": true, "presentation": {"hidden": false, "group": "Next.js", "order": 2}}]}