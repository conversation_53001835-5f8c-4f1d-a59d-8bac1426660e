'use client';

import { CoreFeaturesSection } from '@/components/core-features-section';
import { 
  Shield, 
  Zap, 
  Users, 
  TrendingUp, 
  Award,
  Clock,
  Globe,
  Heart,
  CheckCircle,
  Star,
  Sparkles,
  Target
} from 'lucide-react';
import { useTheme } from 'next-themes';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';

export default function CoreFeaturesIconsTestPage() {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const t = useTranslations("HomePage");

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  const isDark = theme === 'dark';

  // Test data for the 4 core features
  const testFeatures = [
    {
      icon: Shield,
      title: "Store Protection",
      description: "Members enjoy store whitelist protection mechanism",
      color: "from-blue-500 to-blue-600",
      bgColor: "from-blue-50 to-blue-100",
      details: "Multi-layer security protection with fund escrow guarantee",
      benefits: [
        "Approved whitelist stores protection",
        "Administrator review process", 
        "Different membership quotas",
        "24-hour review completion"
      ]
    },
    {
      icon: Zap,
      title: "Smart Matching", 
      description: "Global user base with AI-powered matching",
      color: "from-green-500 to-green-600",
      bgColor: "from-green-50 to-green-100",
      details: "Advanced algorithms ensure optimal matching results",
      benefits: [
        "24/7 global user network",
        "AI-powered precise matching",
        "Under 30-minute response time",
        "Quality executor screening"
      ]
    },
    {
      icon: Users,
      title: "Secure Escrow",
      description: "Multi-layer security with fund escrow protection",
      color: "from-purple-500 to-purple-600", 
      bgColor: "from-purple-50 to-purple-100",
      details: "Bank-level security standards ensure fund safety",
      benefits: [
        "Multiple payment methods",
        "Automatic fund release system",
        "SSL encrypted transmission",
        "24/7 risk monitoring"
      ]
    },
    {
      icon: TrendingUp,
      title: "Membership Benefits",
      description: "Multi-tier membership with exclusive privileges",
      color: "from-orange-500 to-orange-600",
      bgColor: "from-orange-50 to-orange-100", 
      details: "Upgrade membership to unlock more features",
      benefits: [
        "Up to 50% commission discounts",
        "Priority customer support",
        "Higher quotas and limits",
        "Early access to new features"
      ]
    },
  ];

  const additionalFeatures = [
    {
      icon: Clock,
      title: "24/7 Customer Service",
      description: "Round-the-clock professional support",
      color: "text-blue-600"
    },
    {
      icon: Award,
      title: "Industry Certification", 
      description: "Authoritative certification guarantee",
      color: "text-green-600"
    },
    {
      icon: Globe,
      title: "Global Coverage",
      description: "Service covers global markets", 
      color: "text-purple-600"
    },
    {
      icon: Heart,
      title: "User First",
      description: "User experience as the core",
      color: "text-red-600"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header with theme info */}
      <div className="bg-card border-b p-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-foreground">Core Features Icons Test</h1>
            <p className="text-muted-foreground">
              Current theme: <strong>{theme}</strong> | Testing icon display and consistency in core features section
            </p>
          </div>
          <button
            type="button"
            onClick={() => setTheme(isDark ? 'light' : 'dark')}
            className="bg-primary text-primary-foreground px-4 py-2 rounded-lg"
          >
            Switch to {isDark ? 'Light' : 'Dark'} Mode
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="py-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid gap-8">
            {/* Test 1: Icon Display Test */}
            <div className="bg-card p-8 rounded-xl border">
              <h2 className="text-2xl font-semibold mb-6 text-card-foreground">Icon Display Test</h2>
              <div className="grid md:grid-cols-4 gap-6">
                {testFeatures.map((feature, index) => (
                  <div key={index} className="text-center">
                    <div className={`mx-auto mb-4 p-4 bg-gradient-to-r ${feature.bgColor} dark:from-gradient-primary-from/20 dark:to-gradient-primary-to/20 rounded-2xl w-fit`}>
                      <feature.icon className={`h-8 w-8 bg-gradient-to-r ${feature.color} bg-clip-text text-transparent`} />
                    </div>
                    <h3 className="font-semibold text-foreground mb-2">{feature.title}</h3>
                    <p className="text-sm text-muted-foreground">{feature.description}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Test 2: Icon Sizing Consistency */}
            <div className="bg-card p-8 rounded-xl border">
              <h2 className="text-2xl font-semibold mb-6 text-card-foreground">Icon Sizing Consistency</h2>
              <div className="grid md:grid-cols-3 gap-6">
                <div>
                  <h3 className="text-lg font-medium mb-4">Small Icons (w-5 h-5)</h3>
                  <div className="flex gap-4">
                    <Shield className="w-5 h-5 text-blue-600" />
                    <Zap className="w-5 h-5 text-green-600" />
                    <Users className="w-5 h-5 text-purple-600" />
                    <TrendingUp className="w-5 h-5 text-orange-600" />
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-medium mb-4">Medium Icons (w-6 h-6)</h3>
                  <div className="flex gap-4">
                    <Shield className="w-6 h-6 text-blue-600" />
                    <Zap className="w-6 h-6 text-green-600" />
                    <Users className="w-6 h-6 text-purple-600" />
                    <TrendingUp className="w-6 h-6 text-orange-600" />
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-medium mb-4">Large Icons (w-8 h-8)</h3>
                  <div className="flex gap-4">
                    <Shield className="w-8 h-8 text-blue-600" />
                    <Zap className="w-8 h-8 text-green-600" />
                    <Users className="w-8 h-8 text-purple-600" />
                    <TrendingUp className="w-8 h-8 text-orange-600" />
                  </div>
                </div>
              </div>
            </div>

            {/* Test 3: Color Scheme Test */}
            <div className="bg-card p-8 rounded-xl border">
              <h2 className="text-2xl font-semibold mb-6 text-card-foreground">Color Scheme Test</h2>
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-medium mb-4">Gradient Background Icons</h3>
                  <div className="grid grid-cols-2 gap-4">
                    {testFeatures.map((feature, index) => (
                      <div key={index} className="text-center">
                        <div className={`mx-auto mb-2 p-3 bg-gradient-to-r ${feature.bgColor} dark:from-gradient-primary-from/20 dark:to-gradient-primary-to/20 rounded-xl w-fit`}>
                          <feature.icon className={`h-6 w-6 bg-gradient-to-r ${feature.color} bg-clip-text text-transparent`} />
                        </div>
                        <p className="text-xs text-muted-foreground">{feature.title}</p>
                      </div>
                    ))}
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-medium mb-4">Solid Color Icons</h3>
                  <div className="grid grid-cols-2 gap-4">
                    {additionalFeatures.map((feature, index) => (
                      <div key={index} className="text-center">
                        <div className="mx-auto mb-2 p-3 bg-muted rounded-xl w-fit">
                          <feature.icon className={`h-6 w-6 ${feature.color}`} />
                        </div>
                        <p className="text-xs text-muted-foreground">{feature.title}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Test 4: Theme Consistency Test */}
            <div className="bg-card p-8 rounded-xl border">
              <h2 className="text-2xl font-semibold mb-6 text-card-foreground">Theme Consistency Test</h2>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium mb-3">Light Theme Simulation</h3>
                  <div className="bg-white p-4 rounded-lg border">
                    <div className="grid grid-cols-2 gap-3">
                      {testFeatures.slice(0, 4).map((feature, index) => (
                        <div key={index} className="text-center">
                          <div className={`mx-auto mb-2 p-2 bg-gradient-to-r ${feature.bgColor} rounded-lg w-fit`}>
                            <feature.icon className={`h-5 w-5 bg-gradient-to-r ${feature.color} bg-clip-text text-transparent`} />
                          </div>
                          <p className="text-xs text-gray-600">{feature.title}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium mb-3">Dark Theme Simulation</h3>
                  <div className="bg-gray-900 p-4 rounded-lg border border-gray-700">
                    <div className="grid grid-cols-2 gap-3">
                      {testFeatures.slice(0, 4).map((feature, index) => (
                        <div key={index} className="text-center">
                          <div className="mx-auto mb-2 p-2 bg-gradient-to-r from-gradient-primary-from/20 to-gradient-primary-to/20 rounded-lg w-fit">
                            <feature.icon className={`h-5 w-5 bg-gradient-to-r ${feature.color} bg-clip-text text-transparent`} />
                          </div>
                          <p className="text-xs text-gray-300">{feature.title}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Test 5: Icon Appropriateness Check */}
            <div className="bg-card p-8 rounded-xl border">
              <h2 className="text-2xl font-semibold mb-6 text-card-foreground">Icon Appropriateness Check</h2>
              <div className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4 text-sm">
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                        <Shield className="w-5 h-5 text-blue-600" />
                      </div>
                      <div>
                        <span className="font-medium">Shield Icon</span>
                        <p className="text-muted-foreground">Perfect for "Store Protection" - represents security and safety</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                        <Zap className="w-5 h-5 text-green-600" />
                      </div>
                      <div>
                        <span className="font-medium">Zap Icon</span>
                        <p className="text-muted-foreground">Ideal for "Smart Matching" - represents speed and efficiency</p>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                        <Users className="w-5 h-5 text-purple-600" />
                      </div>
                      <div>
                        <span className="font-medium">Users Icon</span>
                        <p className="text-muted-foreground">Great for "Secure Escrow" - represents trust and community</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                        <TrendingUp className="w-5 h-5 text-orange-600" />
                      </div>
                      <div>
                        <span className="font-medium">TrendingUp Icon</span>
                        <p className="text-muted-foreground">Perfect for "Membership Benefits" - represents growth and profit</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Test 6: Hover Effects Test */}
            <div className="bg-card p-8 rounded-xl border">
              <h2 className="text-2xl font-semibold mb-6 text-card-foreground">Hover Effects Test</h2>
              <div className="grid md:grid-cols-4 gap-4">
                {testFeatures.map((feature, index) => (
                  <Card key={index} className="hover:shadow-lg transition-all duration-300 cursor-pointer group">
                    <CardHeader className="text-center pb-4">
                      <div className={`mx-auto mb-4 p-3 bg-gradient-to-r ${feature.bgColor} dark:from-gradient-primary-from/20 dark:to-gradient-primary-to/20 rounded-2xl w-fit group-hover:scale-110 transition-all duration-300`}>
                        <feature.icon className={`h-6 w-6 bg-gradient-to-r ${feature.color} bg-clip-text text-transparent`} />
                      </div>
                      <CardTitle className="text-lg">{feature.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="text-center">
                        {feature.description}
                      </CardDescription>
                    </CardContent>
                  </Card>
                ))}
              </div>
              <p className="text-sm text-muted-foreground mt-4 text-center">
                Hover over the cards above to test icon scaling and hover effects
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Actual Core Features Section for Real-World Testing */}
      <div className="mt-12">
        <h2 className="text-2xl font-bold text-center mb-8 text-foreground">Actual Core Features Section (Real-World Test)</h2>
        <CoreFeaturesSection />
      </div>
    </div>
  );
}
