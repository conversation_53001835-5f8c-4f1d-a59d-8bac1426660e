/**
 * Test endpoint for deposit email functionality
 * This endpoint simulates a successful deposit and tests the email notification system
 */

import { NextRequest, NextResponse } from 'next/server';

import { logDepositEmailDelivery } from '@/lib/email-logging';
import { notifyDepositSuccess, notifyDepositFailure } from '@/lib/financial-email-integration';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      userId, 
      email, 
      name, 
      language = 'zh', 
      amount = 100, 
      testType = 'success' 
    } = body;

    if (!userId || !email) {
      return NextResponse.json(
        { error: 'userId and email are required' },
        { status: 400 },
      );
    }

    const testOrderNo = `DEPOSIT_TEST_${Date.now()}`;
    
    const depositResult = {
      success: testType === 'success',
      transactionId: testOrderNo,
      amount,
      currency: 'USD',
      paymentMethod: 'Test',
      processedAt: new Date().toISOString(),
      newBalance: testType === 'success' ? 1000 + amount : 1000,
      failureReason: testType === 'failure' ? 'Test failure simulation' : undefined,
    };

    const user = {
      id: userId,
      name: name || 'Test User',
      email,
      registrationLanguage: language as 'zh' | 'en',
    };

    let emailResult;
    let emailType: 'deposit-success' | 'deposit-failed';

    if (testType === 'success') {
      emailResult = await notifyDepositSuccess(user, depositResult);
      emailType = 'deposit-success';
    } else {
      emailResult = await notifyDepositFailure(user, depositResult);
      emailType = 'deposit-failed';
    }

    // Log the email delivery
    await logDepositEmailDelivery(
      testOrderNo,
      email,
      emailType,
      emailResult,
    );

    return NextResponse.json({
      success: true,
      message: `Test ${testType} email sent`,
      orderNo: testOrderNo,
      emailResult,
      depositResult,
    });

  } catch (error) {
    console.error('Test deposit email error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to send test email',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Deposit Email Test Endpoint',
    usage: {
      method: 'POST',
      body: {
        userId: 'string (required)',
        email: 'string (required)',
        name: 'string (optional)',
        language: 'zh | en (optional, default: zh)',
        amount: 'number (optional, default: 100)',
        testType: 'success | failure (optional, default: success)',
      },
    },
    example: {
      userId: 'test-user-123',
      email: '<EMAIL>',
      name: 'Test User',
      language: 'en',
      amount: 150,
      testType: 'success',
    },
  });
}
