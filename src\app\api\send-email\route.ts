import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { getUserEmailLanguage } from '@/hooks/useEmailTranslation';
import {
  sendEmail,
  sendNewUserNotification,
  sendVerificationCodeEmail,
  sendWithdrawalApprovedEmail,
  sendWithdrawalRejectedEmail,
  sendTaskCompletedPublisherEmail,
  sendTaskCompletedAccepterEmail,
  sendDepositSuccessEmail,
  sendDepositFailedEmail,
  sendTaskCancelledPublisherEmail,
  sendTaskCancelledAccepterEmail,
  sendTaskAcceptedPublisherEmail,
  generateVerificationCode,
  isValidEmail,
  type EmailOptions,
  type NotificationEmailData,
  type VerificationCodeData,
  type WithdrawalApprovedEmailData,
  type WithdrawalRejectedEmailData,
  type TaskCompletedPublisherEmailData,
  type TaskCompletedAccepterEmailData,
} from '@/lib/email';

// 请求体验证 schema
const sendEmailSchema = z.object({
  type: z.enum([
    'custom',
    'notification',
    'verification',
    'withdrawal-approved',
    'withdrawal-rejected',
    'task-completed-publisher',
    'task-completed-accepter',
    'deposit-success',
    'deposit-failed',
    'task-cancelled-publisher',
    'task-cancelled-accepter',
    'task-accepted-publisher',
  ]),
  to: z.union([z.string().email(), z.array(z.string().email())]),
  data: z.object({}).passthrough().optional(),
  subject: z.string().optional(),
  html: z.string().optional(),
  text: z.string().optional(),
  from: z.string().email().optional(),
});

const notificationEmailSchema = z.object({
  type: z.literal('notification'),
  to: z.string().email(),
  data: z.object({
    userName: z.string(),
    userEmail: z.string().email(),
    signInTime: z.string(),
  }),
});

const customEmailSchema = z.object({
  type: z.literal('custom'),
  to: z.union([z.string().email(), z.array(z.string().email())]),
  subject: z.string().min(1),
  html: z.string().optional(),
  text: z.string().optional(),
  from: z.string().email().optional(),
});

const verificationEmailSchema = z.object({
  type: z.literal('verification'),
  to: z.string().email(),
  data: z.object({
    userName: z.string(),
    userEmail: z.string().email(),
    action: z.enum([
      'login',
      'register',
      'reset-password',
      'verify-current-email',
      'change-email',
    ]),
    expiresIn: z.number().optional(),
    verificationCode: z.string().optional(), // 如果不提供，会自动生成
    language: z.enum(['zh', 'en']).optional(), // 可选的语言参数
  }),
});

const withdrawalApprovedEmailSchema = z.object({
  type: z.literal('withdrawal-approved'),
  to: z.string().email(),
  data: z.object({
    userName: z.string(),
    userEmail: z.string().email(),
    amount: z.number().positive(),
    fee: z.number().min(0), // 添加手续费验证
    actualAmount: z.number().positive(), // 添加到账金额验证
    currency: z.string(),
    withdrawalMethod: z.string(),
    processedAt: z.string(),
    transactionId: z.string().optional(),
    estimatedArrival: z.string().optional(),
  }),
});

const withdrawalRejectedEmailSchema = z.object({
  type: z.literal('withdrawal-rejected'),
  to: z.string().email(),
  data: z.object({
    userName: z.string(),
    userEmail: z.string().email(),
    amount: z.number().positive(),
    currency: z.string(),
    withdrawalMethod: z.string(),
    processedAt: z.string(),
    rejectionReason: z.string(),
    transactionId: z.string().optional(),
  }),
});

const taskCompletedPublisherEmailSchema = z.object({
  type: z.literal('task-completed-publisher'),
  to: z.string().email(),
  data: z.object({
    publisherName: z.string(),
    publisherEmail: z.string().email(),
    taskId: z.string(),
    platform: z.string(),
    category: z.string(),
    quantity: z.number().positive(),
    unitPrice: z.number().positive(),
    totalAmount: z.number().positive(),
    completedAt: z.string(),
  }),
});

const taskCompletedAccepterEmailSchema = z.object({
  type: z.literal('task-completed-accepter'),
  to: z.string().email(),
  data: z.object({
    accepterName: z.string(),
    accepterEmail: z.string().email(),
    taskId: z.string(),
    platform: z.string(),
    category: z.string(),
    quantity: z.number().positive(),
    unitPrice: z.number().positive(),
    totalAmount: z.number().positive(),
    completedAt: z.string(),
    commissionEarned: z.number().positive(),
    depositReleased: z.number().positive(),
    totalEarned: z.number().positive(),
  }),
});

const depositSuccessEmailSchema = z.object({
  type: z.literal('deposit-success'),
  to: z.string().email(),
  data: z.object({
    userName: z.string(),
    userEmail: z.string().email(),
    amount: z.number(),
    currency: z.string(),
    transactionId: z.string(),
    paymentMethod: z.string(),
    processedAt: z.string(),
    newBalance: z.number(),
    language: z.enum(['zh', 'en']).optional(),
  }),
});

const depositFailedEmailSchema = z.object({
  type: z.literal('deposit-failed'),
  to: z.string().email(),
  data: z.object({
    userName: z.string(),
    userEmail: z.string().email(),
    amount: z.number(),
    currency: z.string(),
    transactionId: z.string().optional(),
    paymentMethod: z.string(),
    failureReason: z.string(),
    failedAt: z.string(),
    language: z.enum(['zh', 'en']).optional(),
  }),
});

const taskCancelledPublisherEmailSchema = z.object({
  type: z.literal('task-cancelled-publisher'),
  to: z.string().email(),
  data: z.object({
    userName: z.string(),
    userEmail: z.string().email(),
    taskId: z.string(),
    taskTitle: z.string(),
    cancelledAt: z.string(),
    refundAmount: z.number().optional(),
    currency: z.string().optional(),
    cancellationReason: z.string().optional(),
    language: z.enum(['zh', 'en']).optional(),
  }),
});

const taskCancelledAccepterEmailSchema = z.object({
  type: z.literal('task-cancelled-accepter'),
  to: z.string().email(),
  data: z.object({
    userName: z.string(),
    userEmail: z.string().email(),
    taskId: z.string(),
    taskTitle: z.string(),
    publisherName: z.string(),
    cancelledAt: z.string(),
    penaltyAmount: z.number().optional(),
    currency: z.string().optional(),
    cancellationReason: z.string().optional(),
    language: z.enum(['zh', 'en']).optional(),
  }),
});

const taskAcceptedPublisherEmailSchema = z.object({
  type: z.literal('task-accepted-publisher'),
  to: z.string().email(),
  data: z.object({
    userName: z.string(),
    userEmail: z.string().email(),
    taskId: z.string(),
    taskTitle: z.string(),
    accepterName: z.string(),
    accepterEmail: z.string().email(),
    acceptedAt: z.string(),
    taskReward: z.number(),
    currency: z.string(),
    language: z.enum(['zh', 'en']).optional(),
  }),
});

/**
 * 从请求头检测用户语言偏好
 */
function detectUserLanguage(request: NextRequest): 'zh' | 'en' {
  // 1. 检查 URL 参数中的语言设置
  const url = new URL(request.url);
  const langParam = url.searchParams.get('lang');
  if (langParam === 'zh' || langParam === 'en') {
    return langParam;
  }

  // 2. 检查 Accept-Language 头
  const acceptLanguage = request.headers.get('accept-language');
  if (acceptLanguage) {
    // 简单的语言检测逻辑
    if (acceptLanguage.includes('zh')) {
      return 'zh';
    }
  }

  // 3. 默认返回英文
  return 'en';
}

// 处理提现拒绝邮件的POST请求处理函数
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // 基础验证
    const baseValidation = sendEmailSchema.safeParse(body);
    if (!baseValidation.success) {
      return NextResponse.json(
        {
          success: false,
          error: '请求参数验证失败',
          details: baseValidation.error.errors,
        },
        { status: 400 },
      );
    }

    const { type, to, data, subject, html, text, from } = baseValidation.data;

    // 根据邮件类型处理
    switch (type) {
      case 'notification': {
        const validation = notificationEmailSchema.safeParse(body);
        if (!validation.success) {
          return NextResponse.json(
            {
              success: false,
              error: '通知邮件参数验证失败',
              details: validation.error.errors,
            },
            { status: 400 },
          );
        }

        const result = await sendNewUserNotification(
          validation.data.to,
          validation.data.data as NotificationEmailData,
        );

        return NextResponse.json(result, {
          status: result.success ? 200 : 500,
        });
      }

      case 'custom': {
        const validation = customEmailSchema.safeParse(body);
        if (!validation.success) {
          return NextResponse.json(
            {
              success: false,
              error: '自定义邮件参数验证失败',
              details: validation.error.errors,
            },
            { status: 400 },
          );
        }

        if (!validation.data.html && !validation.data.text) {
          return NextResponse.json(
            {
              success: false,
              error: '邮件内容不能为空，请提供 html 或 text',
            },
            { status: 400 },
          );
        }

        const emailOptions: EmailOptions = {
          to: validation.data.to,
          subject: validation.data.subject,
          html: validation.data.html,
          text: validation.data.text,
          from: validation.data.from,
        };

        const result = await sendEmail(emailOptions);

        return NextResponse.json(result, {
          status: result.success ? 200 : 500,
        });
      }

      case 'verification': {
        const validation = verificationEmailSchema.safeParse(body);
        if (!validation.success) {
          const userLanguage = detectUserLanguage(request);
          const errorMessage =
            userLanguage === 'zh'
              ? '验证码邮件参数验证失败'
              : 'Verification email parameter validation failed';
          return NextResponse.json(
            {
              success: false,
              error: errorMessage,
              details: validation.error.errors,
            },
            { status: 400 },
          );
        }

        // 如果没有提供验证码，自动生成一个
        const verificationCode =
          validation.data.data.verificationCode || generateVerificationCode();

        // 确定用户语言：优先使用请求中的语言，然后从数据库获取，最后使用请求头检测
        let userLanguage: 'zh' | 'en' = validation.data.data.language || 'en';

        if (!validation.data.data.language) {
          try {
            // 尝试从数据库获取用户的注册语言
            userLanguage = await getUserEmailLanguage(
              validation.data.data.userEmail,
            );
          } catch (error) {
            // 如果获取失败，使用请求头检测
            userLanguage = detectUserLanguage(request);
          }
        }

        const verificationData: VerificationCodeData = {
          userName: validation.data.data.userName,
          userEmail: validation.data.data.userEmail,
          verificationCode,
          action: validation.data.data.action,
          expiresIn: validation.data.data.expiresIn || 10, // 默认10分钟
          language: userLanguage, // 添加语言设置
        };

        const result = await sendVerificationCodeEmail(
          validation.data.to,
          verificationData,
          {
            request: request, // 传递请求信息用于语言检测
          }
        );

        // 返回结果时包含生成的验证码（仅用于开发/测试）
        return NextResponse.json(
          {
            ...result,
            verificationCode:
              process.env.NODE_ENV === 'development'
                ? verificationCode
                : undefined,
            language: userLanguage, // 返回使用的语言
          },
          {
            status: result.success ? 200 : 500,
          },
        );
      }

      case 'withdrawal-approved': {
        const validation = withdrawalApprovedEmailSchema.safeParse(body);
        if (!validation.success) {
          return NextResponse.json(
            {
              success: false,
              error: '提现审核通过邮件参数验证失败',
              details: validation.error.errors,
            },
            { status: 400 },
          );
        }

        // 获取用户语言偏好
        const userLanguage = await getUserEmailLanguage(
          validation.data.data.userEmail,
        );
        const emailData = {
          ...validation.data.data,
          language: userLanguage,
        };

        const result = await sendWithdrawalApprovedEmail(
          validation.data.to,
          emailData,
        );

        return NextResponse.json(result, {
          status: result.success ? 200 : 500,
        });
      }

      case 'withdrawal-rejected': {
        const validation = withdrawalRejectedEmailSchema.safeParse(body);
        if (!validation.success) {
          return NextResponse.json(
            {
              success: false,
              error: '提现拒绝邮件参数验证失败',
              details: validation.error.errors,
            },
            { status: 400 },
          );
        }

        // 获取用户语言偏好
        const userLanguage = await getUserEmailLanguage(
          validation.data.data.userEmail,
        );
        const emailData = {
          ...validation.data.data,
          language: userLanguage,
        };

        const result = await sendWithdrawalRejectedEmail(
          validation.data.to,
          emailData,
        );

        return NextResponse.json(result, {
          status: result.success ? 200 : 500,
        });
      }

      case 'task-completed-publisher': {
        const validation = taskCompletedPublisherEmailSchema.safeParse(body);
        if (!validation.success) {
          return NextResponse.json(
            {
              success: false,
              error: '发布者委托完成邮件参数验证失败',
              details: validation.error.errors,
            },
            { status: 400 },
          );
        }

        const result = await sendTaskCompletedPublisherEmail(
          validation.data.to,
          validation.data.data as TaskCompletedPublisherEmailData,
        );

        return NextResponse.json(result, {
          status: result.success ? 200 : 500,
        });
      }

      case 'task-completed-accepter': {
        const validation = taskCompletedAccepterEmailSchema.safeParse(body);
        if (!validation.success) {
          return NextResponse.json(
            {
              success: false,
              error: '接单者委托完成邮件参数验证失败',
              details: validation.error.errors,
            },
            { status: 400 },
          );
        }

        const result = await sendTaskCompletedAccepterEmail(
          validation.data.to,
          validation.data.data as TaskCompletedAccepterEmailData,
        );

        return NextResponse.json(result, {
          status: result.success ? 200 : 500,
        });
      }

      case 'deposit-success': {
        const validation = depositSuccessEmailSchema.safeParse(body);
        if (!validation.success) {
          return NextResponse.json(
            {
              success: false,
              error: '充值成功邮件参数验证失败',
              details: validation.error.errors,
            },
            { status: 400 },
          );
        }

        const result = await sendDepositSuccessEmail(
          validation.data.to,
          validation.data.data,
        );

        return NextResponse.json(result, {
          status: result.success ? 200 : 500,
        });
      }

      case 'deposit-failed': {
        const validation = depositFailedEmailSchema.safeParse(body);
        if (!validation.success) {
          return NextResponse.json(
            {
              success: false,
              error: '充值失败邮件参数验证失败',
              details: validation.error.errors,
            },
            { status: 400 },
          );
        }

        const result = await sendDepositFailedEmail(
          validation.data.to,
          validation.data.data,
        );

        return NextResponse.json(result, {
          status: result.success ? 200 : 500,
        });
      }

      case 'task-cancelled-publisher': {
        const validation = taskCancelledPublisherEmailSchema.safeParse(body);
        if (!validation.success) {
          return NextResponse.json(
            {
              success: false,
              error: '委托取消邮件参数验证失败',
              details: validation.error.errors,
            },
            { status: 400 },
          );
        }

        // 获取用户语言偏好
        const userLanguage = await getUserEmailLanguage(
          validation.data.data.userEmail,
        );
        const emailData = {
          ...validation.data.data,
          language: userLanguage,
        };

        const result = await sendTaskCancelledPublisherEmail(
          validation.data.to,
          emailData,
        );

        return NextResponse.json(result, {
          status: result.success ? 200 : 500,
        });
      }

      case 'task-cancelled-accepter': {
        const validation = taskCancelledAccepterEmailSchema.safeParse(body);
        if (!validation.success) {
          return NextResponse.json(
            {
              success: false,
              error: '委托取消邮件参数验证失败',
              details: validation.error.errors,
            },
            { status: 400 },
          );
        }

        // 获取用户语言偏好
        const userLanguage = await getUserEmailLanguage(
          validation.data.data.userEmail,
        );
        const emailData = {
          ...validation.data.data,
          language: userLanguage,
        };

        const result = await sendTaskCancelledAccepterEmail(
          validation.data.to,
          emailData,
        );

        return NextResponse.json(result, {
          status: result.success ? 200 : 500,
        });
      }

      case 'task-accepted-publisher': {
        const validation = taskAcceptedPublisherEmailSchema.safeParse(body);
        if (!validation.success) {
          return NextResponse.json(
            {
              success: false,
              error: '委托接受邮件参数验证失败',
              details: validation.error.errors,
            },
            { status: 400 },
          );
        }

        // 获取用户语言偏好
        const userLanguage = await getUserEmailLanguage(
          validation.data.data.userEmail,
        );
        const emailData = {
          ...validation.data.data,
          language: userLanguage,
        };

        const result = await sendTaskAcceptedPublisherEmail(
          validation.data.to,
          emailData,
        );

        return NextResponse.json(result, {
          status: result.success ? 200 : 500,
        });
      }

      default:
        return NextResponse.json(
          { success: false, error: '不支持的邮件类型' },
          { status: 400 },
        );
    }
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: '服务器内部错误',
        details: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 },
    );
  }
}

// GET 方法用于测试邮件服务状态
export async function GET() {
  try {
    // 检查环境变量配置
    const hasApiKey = !!process.env.RESEND_API_KEY;
    const hasFromEmail = !!process.env.RESEND_FROM;

    return NextResponse.json({
      success: true,
      status: 'Email service is available',
      config: {
        hasApiKey,
        hasFromEmail,
        fromEmail: hasFromEmail ? process.env.RESEND_FROM : null,
      },
    });
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: '邮件服务配置检查失败',
        details: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 },
    );
  }
}
