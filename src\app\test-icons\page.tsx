"use client";

import {
  Eye,
  FileText,
  HandHeart,
  Zap,
  <PERSON>tings,
  CheckCircle2,
  TrendingUp,
  <PERSON><PERSON>,
  Star,
  <PERSON>rkles,
  Target
} from "lucide-react";

// 复合图标组件 - 精美设计版本
const CompositeIcon = ({ 
  MainIcon, 
  AccentIcon, 
  DecoIcon, 
  className = "",
  gradientColors = "from-blue-500 to-purple-600"
}: { 
  MainIcon: any; 
  AccentIcon?: any; 
  DecoIcon?: any; 
  className?: string;
  gradientColors?: string;
}) => (
  <div className={`relative group ${className}`}>
    {/* 背景光晕效果 - 多层渐变 */}
    <div className={`absolute inset-0 bg-gradient-to-r ${gradientColors} opacity-20 rounded-full blur-md scale-125 group-hover:scale-150 transition-all duration-500 -z-10`} />
    <div className={`absolute inset-0 bg-gradient-to-r ${gradientColors} opacity-10 rounded-full blur-lg scale-150 group-hover:scale-175 transition-all duration-700 -z-20`} />
    
    {/* 主图标容器 */}
    <div className="relative">
      {/* 主图标 */}
      <MainIcon className={`h-8 w-8 relative z-10 bg-gradient-to-r ${gradientColors} bg-clip-text text-transparent group-hover:scale-110 transition-all duration-300`} />
      
      {/* 右上角辅助图标 */}
      {AccentIcon && (
        <div className="absolute -top-1.5 -right-1.5 z-20">
          <div className="bg-white rounded-full p-1 shadow-lg border border-gray-100 group-hover:scale-110 transition-all duration-300">
            <AccentIcon className="h-3 w-3 text-gray-600" />
          </div>
        </div>
      )}
      
      {/* 左下角装饰图标 */}
      {DecoIcon && (
        <div className="absolute -bottom-1 -left-1 z-20">
          <div className="bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full p-0.5 shadow-sm">
            <DecoIcon className="h-2.5 w-2.5 text-white animate-pulse" />
          </div>
        </div>
      )}
      
      {/* 动态光效 */}
      <div className="absolute inset-0 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-500">
        <div className={`absolute top-0 right-0 w-1 h-1 bg-gradient-to-r ${gradientColors} rounded-full animate-ping`} />
        <div className={`absolute bottom-1 left-1 w-0.5 h-0.5 bg-gradient-to-r ${gradientColors} rounded-full animate-pulse`} />
        <div className={`absolute top-2 left-0 w-0.5 h-0.5 bg-gradient-to-r ${gradientColors} rounded-full animate-bounce`} />
      </div>
    </div>
  </div>
);

export default function TestIconsPage() {
  const completeTaskSteps = [
    {
      icon: () => (
        <CompositeIcon 
          MainIcon={Eye} 
          AccentIcon={FileText} 
          DecoIcon={Sparkles}
          gradientColors="from-blue-500 to-blue-600"
        />
      ),
      title: "浏览任务",
      description: "Browse Tasks",
      details: "浏览查看可接受的任务，发现最适合的机会",
      color: "from-blue-500 to-blue-600",
      bgColor: "from-blue-50 to-blue-100"
    },
    {
      icon: () => (
        <CompositeIcon 
          MainIcon={HandHeart} 
          AccentIcon={Target} 
          DecoIcon={Zap}
          gradientColors="from-green-500 to-green-600"
        />
      ),
      title: "接受任务",
      description: "Accept Tasks", 
      details: "精准选择适合的任务，快速接单行动",
      color: "from-green-500 to-green-600",
      bgColor: "from-green-50 to-green-100"
    },
    {
      icon: () => (
        <CompositeIcon 
          MainIcon={Settings} 
          AccentIcon={CheckCircle2} 
          DecoIcon={TrendingUp}
          gradientColors="from-purple-500 to-purple-600"
        />
      ),
      title: "执行任务",
      description: "Execute Tasks",
      details: "高效执行任务内容，确保质量完成",
      color: "from-purple-500 to-purple-600",
      bgColor: "from-purple-50 to-purple-100"
    },
    {
      icon: () => (
        <CompositeIcon 
          MainIcon={Coins} 
          AccentIcon={TrendingUp} 
          DecoIcon={Star}
          gradientColors="from-orange-500 to-orange-600"
        />
      ),
      title: "获得报酬",
      description: "Get Rewards",
      details: "获得丰厚报酬，享受优质收益体验",
      color: "from-orange-500 to-orange-600",
      bgColor: "from-orange-50 to-orange-100"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            完成任务流程 - 新图标设计
          </h1>
          <p className="text-xl text-gray-600">
            精美的复合图标设计，具有视觉冲击力和现代感
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {completeTaskSteps.map((step, index) => (
            <div key={index} className="relative group">
              <div className="h-full min-h-[320px] bg-white border-0 shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden flex flex-col rounded-2xl">
                <div className={`h-2 bg-gradient-to-r ${step.color} flex-shrink-0`} />
                <div className="text-center pb-4 pt-8 flex-grow flex flex-col justify-between p-6">
                  <div className="space-y-4">
                    <div className="relative mb-6">
                      <div className={`mx-auto p-4 bg-gradient-to-r ${step.bgColor} rounded-2xl w-fit group-hover:scale-110 transition-all duration-300`}>
                        <step.icon />
                      </div>
                      <div className="absolute -top-2 -right-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold shadow-lg">
                        {index + 1}
                      </div>
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3 leading-tight">
                      {step.title}
                    </h3>
                  </div>
                  <div className="space-y-3 mt-auto">
                    <p className="text-gray-600 leading-relaxed text-base">
                      {step.description}
                    </p>
                    <p className="text-sm text-gray-500 font-medium leading-relaxed">
                      {step.details}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-8 text-white">
            <h2 className="text-2xl font-bold mb-4">设计特点</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-left">
              <div>
                <h3 className="font-semibold mb-2">复合图标设计</h3>
                <p className="text-sm opacity-90">主图标 + 辅助图标 + 装饰元素的组合设计</p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">多层光晕效果</h3>
                <p className="text-sm opacity-90">渐变背景光晕，hover时动态缩放</p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">动态交互效果</h3>
                <p className="text-sm opacity-90">光点动画、脉冲效果、弹跳动画</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
