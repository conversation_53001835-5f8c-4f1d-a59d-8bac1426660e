'use client';

import { RefundGoLogo, RefundGoLogoCompact, RefundGoLogoIcon, RefundGoLogoHomepage } from '@/components/refund-go-logo';

export default function LogoDemoPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">RefundGo Logo Variants</h1>
          <p className="text-lg text-gray-600">
            Showcasing the different logo implementations throughout the application
          </p>
        </div>

        <div className="grid gap-8">
          {/* Homepage Version - Gradient Animation Version #4 */}
          <div className="bg-white p-8 rounded-xl shadow-lg">
            <h2 className="text-2xl font-semibold mb-6 text-gray-800">Homepage Version (Gradient Animation #4)</h2>
            <p className="text-gray-600 mb-6">
              专门为首页设计的#4 Gradient Animation Version，包含完整的渐变扫描动画、图标旋转和颜色变化效果。
            </p>
            <div className="flex justify-center mb-6">
              <RefundGoLogoHomepage />
            </div>
            <div className="text-sm text-gray-500">
              <strong>特色功能：</strong> 渐变扫描动画、图标旋转(-45°)、绿色圆点缩放、文字颜色渐变、标语显示
            </div>
          </div>

          {/* Full Animated Version */}
          <div className="bg-white p-8 rounded-xl shadow-lg">
            <h2 className="text-2xl font-semibold mb-6 text-gray-800">Full Animated Version</h2>
            <p className="text-gray-600 mb-6">
              Primary logo with gradient animation and tagline. Used in headers and landing pages.
            </p>
            <div className="flex flex-wrap gap-6">
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">Large Size</h3>
                <RefundGoLogo variant="full" size="lg" animated={true} showTagline={true} />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">Medium Size</h3>
                <RefundGoLogo variant="full" size="md" animated={true} showTagline={true} />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">Small Size</h3>
                <RefundGoLogo variant="full" size="sm" animated={true} showTagline={true} />
              </div>
            </div>
          </div>

          {/* Compact Version */}
          <div className="bg-white p-8 rounded-xl shadow-lg">
            <h2 className="text-2xl font-semibold mb-6 text-gray-800">Compact Version</h2>
            <p className="text-gray-600 mb-6">
              Compact logo without tagline. Used in navigation bars and headers.
            </p>
            <div className="flex flex-wrap gap-6 items-center">
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">Animated</h3>
                <RefundGoLogoCompact animated={true} />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">Static</h3>
                <RefundGoLogoCompact animated={false} />
              </div>
            </div>
          </div>

          {/* Icon Only Version */}
          <div className="bg-white p-8 rounded-xl shadow-lg">
            <h2 className="text-2xl font-semibold mb-6 text-gray-800">Icon Only Version</h2>
            <p className="text-gray-600 mb-6">
              Icon-only version for sidebars and compact spaces.
            </p>
            <div className="flex flex-wrap gap-6 items-center">
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">Medium Size</h3>
                <RefundGoLogoIcon size="md" />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">Small Size</h3>
                <RefundGoLogoIcon size="sm" />
              </div>
            </div>
          </div>

          {/* Static Version */}
          <div className="bg-white p-8 rounded-xl shadow-lg">
            <h2 className="text-2xl font-semibold mb-6 text-gray-800">Static Version</h2>
            <p className="text-gray-600 mb-6">
              Static version without animations. Used in emails, print, and performance-sensitive contexts.
            </p>
            <div className="flex flex-wrap gap-6">
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">With Tagline</h3>
                <RefundGoLogo variant="static" size="md" showTagline={true} />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">Without Tagline</h3>
                <RefundGoLogo variant="static" size="md" showTagline={false} />
              </div>
            </div>
          </div>

          {/* Usage Examples */}
          <div className="bg-white p-8 rounded-xl shadow-lg">
            <h2 className="text-2xl font-semibold mb-6 text-gray-800">Usage Examples</h2>
            
            {/* Navbar Example */}
            <div className="mb-8">
              <h3 className="text-lg font-medium mb-4 text-gray-700">Navigation Bar</h3>
              <div className="bg-gray-100 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <RefundGoLogoCompact animated={true} />
                  <div className="flex items-center gap-4">
                    <span className="text-sm text-gray-600">Menu Items</span>
                    <button className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">
                      Sign In
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Sidebar Example */}
            <div className="mb-8">
              <h3 className="text-lg font-medium mb-4 text-gray-700">Sidebar</h3>
              <div className="bg-gray-100 p-4 rounded-lg">
                <div className="flex items-center gap-3">
                  <RefundGoLogoIcon size="sm" />
                  <div>
                    <div className="font-semibold text-sm">RefundGo</div>
                    <div className="text-xs text-gray-500">Premium Member</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer Example */}
            <div>
              <h3 className="text-lg font-medium mb-4 text-gray-700">Footer</h3>
              <div className="bg-gray-900 text-white p-6 rounded-lg">
                <RefundGoLogo 
                  variant="static" 
                  size="md" 
                  showTagline={true}
                  className="bg-transparent shadow-none text-white"
                />
                <p className="text-gray-300 mt-4 text-sm">
                  Professional refund services for your business needs.
                </p>
              </div>
            </div>
          </div>

          {/* Performance Notes */}
          <div className="bg-blue-50 p-8 rounded-xl border border-blue-200">
            <h2 className="text-2xl font-semibold mb-6 text-blue-900">Performance & Accessibility</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium mb-3 text-blue-800">Performance Features</h3>
                <ul className="space-y-2 text-blue-700">
                  <li>• GPU-accelerated animations</li>
                  <li>• Optimized CSS transitions</li>
                  <li>• Reduced motion support</li>
                  <li>• Static variants for performance-sensitive contexts</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-3 text-blue-800">Accessibility Features</h3>
                <ul className="space-y-2 text-blue-700">
                  <li>• High contrast mode support</li>
                  <li>• Keyboard navigation focus styles</li>
                  <li>• Screen reader friendly</li>
                  <li>• Print-optimized styles</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
