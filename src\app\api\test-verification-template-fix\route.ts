/**
 * Test Endpoint for Verification Template Language Consistency Fix
 * Tests that both email subject and content are in the same language
 */

import { NextRequest, NextResponse } from 'next/server';

import { getUserEmailLanguage } from '@/lib/email-language-detection';
import { getEmailTranslations } from '@/lib/email-translations';
import { sendEmail } from '@/lib/email';
import {
  verificationCodeTemplateI18n,
  type VerificationCodeData,
} from '@/lib/email-templates/verification-code-i18n';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      testScenarios = ['all']
    } = body;

    const results = [];

    // Test Scenario 1: English URL should produce English subject AND content
    if (testScenarios.includes('all') || testScenarios.includes('english-consistency')) {
      console.log('\n🧪 Testing English URL Language Consistency');
      
      const mockHeaders1 = new Headers();
      mockHeaders1.set('referer', 'https://refundgo.org/en/sign-up');
      mockHeaders1.set('accept-language', ''); // Remove to test URL priority
      
      const mockRequest1 = {
        ...request,
        headers: mockHeaders1,
        url: request.url,
      } as NextRequest;

      const language1 = await getUserEmailLanguage(
        'test-user-en-consistency',
        mockRequest1,
        'zh'
      );

      const translations1 = getEmailTranslations(language1);
      const verificationCode1 = '123456';

      const emailData1: VerificationCodeData = {
        userName: 'Test User',
        userEmail: '<EMAIL>',
        verificationCode: verificationCode1,
        expiresIn: 10,
        action: 'register',
        language: language1,
      };

      const emailSubject1 = translations1.notifications.verification.register.subject;
      const emailHtml1 = verificationCodeTemplateI18n(emailData1);

      // Check if both subject and content are in English
      const subjectInEnglish = emailSubject1.includes('Registration Verification Code');
      const contentInEnglish = emailHtml1.includes('Welcome to RefundGo') &&
                              emailHtml1.includes('Verification Code') &&
                              emailHtml1.includes('minutes') &&
                              emailHtml1.includes('Best regards') &&
                              emailHtml1.includes('RefundGo Team') &&
                              !emailHtml1.includes('您好') &&
                              !emailHtml1.includes('验证码');

      results.push({
        scenario: 'English URL Consistency Test',
        description: 'Both subject and content should be in English for /en/sign-up',
        inputs: {
          referer: 'https://refundgo.org/en/sign-up',
          action: 'register',
        },
        detectedLanguage: language1,
        emailSubject: emailSubject1,
        subjectCorrect: subjectInEnglish,
        contentCorrect: contentInEnglish,
        bothConsistent: subjectInEnglish && contentInEnglish,
        passed: subjectInEnglish && contentInEnglish,
      });
    }

    // Test Scenario 2: Chinese URL should produce Chinese subject AND content
    if (testScenarios.includes('all') || testScenarios.includes('chinese-consistency')) {
      console.log('\n🧪 Testing Chinese URL Language Consistency');
      
      const mockHeaders2 = new Headers();
      mockHeaders2.set('referer', 'https://refundgo.org/zh/sign-in');
      mockHeaders2.set('accept-language', ''); // Remove to test URL priority
      
      const mockRequest2 = {
        ...request,
        headers: mockHeaders2,
        url: request.url,
      } as NextRequest;

      const language2 = await getUserEmailLanguage(
        'test-user-zh-consistency',
        mockRequest2,
        'zh'
      );

      const translations2 = getEmailTranslations(language2);
      const verificationCode2 = '654321';

      const emailData2: VerificationCodeData = {
        userName: '测试用户',
        userEmail: '<EMAIL>',
        verificationCode: verificationCode2,
        expiresIn: 10,
        action: 'login',
        language: language2,
      };

      const emailSubject2 = translations2.notifications.verification.login.subject;
      const emailHtml2 = verificationCodeTemplateI18n(emailData2);

      // Check if both subject and content are in Chinese
      const subjectInChinese = emailSubject2.includes('登录验证码');
      const contentInChinese = emailHtml2.includes('您正在尝试登录') &&
                              emailHtml2.includes('验证码') &&
                              emailHtml2.includes('分钟') &&
                              emailHtml2.includes('此致敬礼') &&
                              emailHtml2.includes('RefundGo 团队') &&
                              !emailHtml2.includes('Hello') &&
                              !emailHtml2.includes('Best regards');

      results.push({
        scenario: 'Chinese URL Consistency Test',
        description: 'Both subject and content should be in Chinese for /zh/sign-in',
        inputs: {
          referer: 'https://refundgo.org/zh/sign-in',
          action: 'login',
        },
        detectedLanguage: language2,
        emailSubject: emailSubject2,
        subjectCorrect: subjectInChinese,
        contentCorrect: contentInChinese,
        bothConsistent: subjectInChinese && contentInChinese,
        passed: subjectInChinese && contentInChinese,
      });
    }

    // Test Scenario 3: Send actual test emails to verify
    if (testScenarios.includes('all') || testScenarios.includes('send-test-emails')) {
      console.log('\n🧪 Sending Test Emails');

      // English test email
      const mockHeaders3 = new Headers();
      mockHeaders3.set('referer', 'https://refundgo.org/en/sign-up');
      
      const mockRequest3 = {
        ...request,
        headers: mockHeaders3,
        url: request.url,
      } as NextRequest;

      const language3 = await getUserEmailLanguage(
        'test-user-email-send',
        mockRequest3,
        'zh'
      );

      const translations3 = getEmailTranslations(language3);
      const emailData3: VerificationCodeData = {
        userName: 'Email Test User',
        userEmail: '<EMAIL>',
        verificationCode: '789012',
        expiresIn: 10,
        action: 'register',
        language: language3,
      };

      try {
        const emailResult = await sendEmail({
          to: '<EMAIL>',
          subject: translations3.notifications.verification.register.subject,
          html: verificationCodeTemplateI18n(emailData3),
        });

        results.push({
          scenario: 'Email Delivery Test',
          description: 'Test actual email delivery with consistent language',
          detectedLanguage: language3,
          emailSent: !!emailResult,
          emailResult: emailResult ? 'Success' : 'Failed',
          passed: !!emailResult,
        });
      } catch (error) {
        results.push({
          scenario: 'Email Delivery Test',
          description: 'Test actual email delivery with consistent language',
          detectedLanguage: language3,
          emailSent: false,
          emailResult: error instanceof Error ? error.message : 'Unknown error',
          passed: false,
        });
      }
    }

    // Calculate overall results
    const totalTests = results.length;
    const passedTests = results.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;

    return NextResponse.json({
      success: true,
      message: 'Verification template language consistency test completed',
      summary: {
        totalTests,
        passedTests,
        failedTests,
        successRate: Math.round((passedTests / totalTests) * 100),
        allConsistent: failedTests === 0,
      },
      issueFixed: {
        problem: 'Email subject in correct language but content in wrong language',
        solution: 'Updated verificationCodeTemplateI18n to use centralized translation system',
        keyChanges: [
          'Replaced embedded translations with getEmailTranslations()',
          'Updated all template text to use centralized translation properties',
          'Ensured both subject and content use same language detection result',
        ],
      },
      testResults: results,
      affectedPages: [
        '/en/sign-up - Registration verification emails',
        '/en/sign-in - Login verification emails',
        '/zh/sign-up - Registration verification emails',
        '/zh/sign-in - Login verification emails',
      ],
    });

  } catch (error) {
    console.error('Verification template test error:', error);
    return NextResponse.json(
      { 
        error: 'Test failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Verification Template Language Consistency Test',
    description: 'Tests that both email subject and content are in the same language',
    issue: {
      problem: 'Email subjects correct but content in wrong language',
      cause: 'Template using embedded translations instead of centralized system',
      solution: 'Updated template to use getEmailTranslations() consistently',
    },
    usage: {
      method: 'POST',
      body: {
        testScenarios: 'array of strings (optional, default: ["all"])',
      },
    },
    availableScenarios: [
      'english-consistency - Test English URL produces English subject and content',
      'chinese-consistency - Test Chinese URL produces Chinese subject and content', 
      'send-test-emails - Send actual test emails to verify delivery',
      'all - Run all test scenarios',
    ],
    examples: {
      runAllTests: {
        testScenarios: ['all'],
      },
      specificTest: {
        testScenarios: ['english-consistency', 'chinese-consistency'],
      },
    },
  });
}
