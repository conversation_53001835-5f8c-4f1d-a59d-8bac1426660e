import DebugTestComponent from '@/components/DebugTestComponent';

export default function DebugTestPage() {
  return (
    <div className="container mx-auto p-8 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-4">VSCode 调试测试页面</h1>
        <p className="text-gray-600 mb-8">
          这个页面用于测试 VSCode 中的 Next.js 调试功能
        </p>
      </div>

      <div className="grid gap-8">
        <div className="bg-blue-50 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">🔧 调试说明</h2>
          <div className="space-y-2 text-sm">
            <p><strong>1. 启动调试服务器:</strong> 运行 <code className="bg-gray-200 px-2 py-1 rounded">npm run dev:debug</code></p>
            <p><strong>2. 设置断点:</strong> 在 VSCode 中点击行号左侧设置断点</p>
            <p><strong>3. 开始调试:</strong> 按 F5 或选择调试配置</p>
            <p><strong>4. 测试功能:</strong> 点击下面的按钮触发断点</p>
          </div>
        </div>

        <div className="bg-green-50 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">🎯 调试目标</h2>
          <div className="grid md:grid-cols-2 gap-4 text-sm">
            <div>
              <h3 className="font-medium mb-2">客户端调试:</h3>
              <ul className="list-disc list-inside space-y-1">
                <li>React 组件状态</li>
                <li>事件处理函数</li>
                <li>useEffect 钩子</li>
                <li>异步操作</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-2">服务端调试:</h3>
              <ul className="list-disc list-inside space-y-1">
                <li>API 路由处理</li>
                <li>请求参数解析</li>
                <li>数据库操作模拟</li>
                <li>错误处理</li>
              </ul>
            </div>
          </div>
        </div>

        <DebugTestComponent />

        <div className="bg-yellow-50 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">📋 调试检查清单</h2>
          <div className="space-y-2 text-sm">
            <label className="flex items-center space-x-2">
              <input type="checkbox" className="rounded" />
              <span>服务端断点正常工作 (API 路由)</span>
            </label>
            <label className="flex items-center space-x-2">
              <input type="checkbox" className="rounded" />
              <span>客户端断点正常工作 (React 组件)</span>
            </label>
            <label className="flex items-center space-x-2">
              <input type="checkbox" className="rounded" />
              <span>变量检查功能正常</span>
            </label>
            <label className="flex items-center space-x-2">
              <input type="checkbox" className="rounded" />
              <span>调用堆栈显示正确</span>
            </label>
            <label className="flex items-center space-x-2">
              <input type="checkbox" className="rounded" />
              <span>控制台输出完整</span>
            </label>
            <label className="flex items-center space-x-2">
              <input type="checkbox" className="rounded" />
              <span>网络请求可以调试</span>
            </label>
          </div>
        </div>

        <div className="bg-gray-50 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">🚀 快速测试</h2>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium mb-2">测试 API 端点:</h3>
              <div className="space-y-1 text-sm font-mono">
                <p>GET: <a href="/api/debug-test" className="text-blue-600 hover:underline">/api/debug-test</a></p>
                <p>GET: <a href="/api/debug-test?limit=2&delay=1000" className="text-blue-600 hover:underline">/api/debug-test?limit=2&delay=1000</a></p>
              </div>
            </div>
            <div>
              <h3 className="font-medium mb-2">建议的断点位置:</h3>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li><code>components/DebugTestComponent.tsx:20</code> - fetchUsers 函数</li>
                <li><code>components/DebugTestComponent.tsx:33</code> - incrementCounter 函数</li>
                <li><code>app/api/debug-test/route.ts:12</code> - API GET 处理</li>
                <li><code>app/api/debug-test/route.ts:35</code> - 返回数据前</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
