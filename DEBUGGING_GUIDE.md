# VSCode Next.js 调试指南

本指南将帮助您在 VSCode 中设置和使用 Next.js 项目的完整调试环境。

## 🚀 快速开始

### 1. 启动调试服务器

```bash
# 启动带调试功能的开发服务器
npm run dev:debug

# 或者启动时立即暂停（用于调试启动过程）
npm run dev:debug-brk
```

### 2. 开始调试

在 VSCode 中按 `F5` 或使用以下调试配置：

- **Next.js: Launch Full Stack (Recommended)** - 同时调试服务端和客户端
- **Next.js: debug server-side** - 仅调试服务端代码
- **Next.js: debug client-side** - 仅调试客户端代码
- **Next.js: debug full stack** - 高级全栈调试模式

## 🔧 调试配置说明

### 服务端调试 (Server-side)
- **端口**: 9229 (主进程), 9230 (路由服务器)
- **类型**: Node.js 调试
- **支持**: API 路由、服务端组件、中间件等

### 客户端调试 (Client-side)
- **浏览器**: Chrome (推荐) 或 Firefox
- **端口**: 3000
- **支持**: React 组件、客户端逻辑、浏览器 API

### 源映射配置
项目已配置完整的源映射支持：
- `webpack://_N_E/*` → 项目根目录
- `webpack:///src/*` → src 目录
- `webpack:///app/*` → app 目录
- `webpack:///components/*` → components 目录

## 🎯 使用技巧

### 设置断点
1. 在代码行号左侧点击设置断点
2. 使用 `debugger;` 语句强制暂停
3. 条件断点：右键断点 → "编辑断点" → 设置条件

### 变量检查
- **变量面板**: 查看当前作用域的所有变量
- **监视面板**: 添加自定义表达式监视
- **悬停检查**: 鼠标悬停在变量上查看值

### 调用堆栈
- 查看函数调用链
- 点击堆栈帧跳转到对应代码
- 支持异步调用堆栈追踪

### 控制台调试
- **调试控制台**: 在断点处执行表达式
- **集成终端**: 查看服务器日志
- **浏览器控制台**: 查看客户端日志

## 🛠️ 高级功能

### 网络请求调试
- 启用了完整 URL 日志记录
- 在调试控制台中查看 fetch 请求详情
- 支持 API 路由断点调试

### 热重载调试
- 代码更改时保持调试会话
- 断点在热重载后保持有效
- 支持组件状态保持

### 多浏览器支持
- Chrome: 最佳调试体验
- Firefox: 备选调试选项
- Edge: 通过 serverReadyAction 支持

## 🔍 故障排除

### 常见问题

**1. 断点不生效**
- 确保源映射正确加载
- 检查文件路径映射
- 重启调试会话

**2. 无法连接调试器**
- 确认端口 9229/9230 未被占用
- 检查防火墙设置
- 重启开发服务器

**3. 变量显示 undefined**
- 检查作用域和时机
- 确认变量已初始化
- 使用监视表达式

**4. 源码映射错误**
- 清除 .next 缓存目录
- 重新启动开发服务器
- 检查 webpack 配置

### 性能优化

**开发模式下的调试性能**
- 使用 `eval-source-map` 获得最佳调试体验
- 避免在生产构建中启用调试
- 必要时可以禁用某些 webpack 优化

## 📝 调试最佳实践

### 1. 分层调试
- 先调试服务端逻辑
- 再调试客户端交互
- 最后进行端到端测试

### 2. 日志策略
- 使用 `console.log` 进行快速调试
- 使用断点进行深入分析
- 结合网络面板查看请求

### 3. 错误处理
- 在 try-catch 块中设置断点
- 使用错误边界调试 React 错误
- 监控异步操作的错误

### 4. 测试集成
- 结合 Jest 调试配置
- 使用 Playwright 进行 E2E 调试
- 单元测试和集成测试并行

## 🎨 VSCode 扩展推荐

为了获得更好的调试体验，建议安装以下扩展：

- **ES7+ React/Redux/React-Native snippets**
- **Auto Rename Tag**
- **Bracket Pair Colorizer**
- **GitLens**
- **Thunder Client** (API 测试)

## 📚 相关资源

- [Next.js 官方调试文档](https://nextjs.org/docs/app/building-your-application/configuring/debugging)
- [VSCode 调试指南](https://code.visualstudio.com/docs/editor/debugging)
- [Node.js 调试指南](https://nodejs.org/en/docs/guides/debugging-getting-started/)

---

**提示**: 如果遇到问题，请检查 VSCode 输出面板中的调试日志，或查看集成终端中的错误信息。
